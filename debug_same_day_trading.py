#!/usr/bin/env python3
"""
调试同一天多笔交易问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
from datetime import datetime, timedelta
from typing import Dict, Any

from trading_system.core import EventEngine, MarketEvent, SignalEvent, OrderEvent, FillEvent
from trading_system.data import HistoricalDataProvider
from trading_system.analysis import IndicatorCalculator
from trading_system.strategies import FixedEnhancedTurnPointStrategy
from trading_system.execution import BacktestExecutionHandler
from trading_system.portfolio import PortfolioManager

class SameDayTradingDebugger:
    """同一天交易调试器"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        
        # 核心组件
        self.event_engine = EventEngine()
        self.data_provider = HistoricalDataProvider(
            symbol='003021',
            start_date='20250620',  # 缩短时间范围
            end_date='20250630'
        )
        self.indicator_calculator = IndicatorCalculator()
        self.strategy = FixedEnhancedTurnPointStrategy('003021')
        self.execution_handler = BacktestExecutionHandler(
            event_engine=self.event_engine,
            slippage_pct=0.001
        )
        self.portfolio_manager = PortfolioManager(initial_cash=100000)
        
        # 调试统计
        self.debug_stats = {
            'signals_by_date': {},
            'trades_by_date': {},
            'cooldown_checks': [],
            'last_trade_times': []
        }
        
        # 注册事件处理器
        self._register_handlers()
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def _register_handlers(self):
        """注册事件处理器"""
        self.event_engine.register_handler(MarketEvent, self._on_market_event)
        self.event_engine.register_handler(SignalEvent, self._on_signal_event)
        self.event_engine.register_handler(OrderEvent, self._on_order_event)
        self.event_engine.register_handler(FillEvent, self._on_fill_event)
    
    def _on_market_event(self, event: MarketEvent):
        """处理市场数据事件"""
        # 计算技术指标
        indicators = self.indicator_calculator.calculate_all(event)
        event.indicators.update(indicators)
        
        # 记录当前日期
        current_date = event.timestamp.strftime('%Y-%m-%d')
        
        # 策略处理前记录状态
        strategy_stats = self.strategy.get_stats()
        last_trade_time = strategy_stats.get('last_trade_time')
        
        if last_trade_time:
            time_diff = event.timestamp - last_trade_time
            cooldown_info = {
                'current_time': event.timestamp,
                'last_trade_time': last_trade_time,
                'time_diff_days': time_diff.days,
                'time_diff_seconds': time_diff.total_seconds(),
                'can_trade': time_diff.days >= 1
            }
            self.debug_stats['cooldown_checks'].append(cooldown_info)
            
            self.logger.info(f"📅 {current_date}: 冷却期检查 - 上次交易: {last_trade_time.strftime('%Y-%m-%d %H:%M:%S')}, "
                           f"时间差: {time_diff.days}天 {time_diff.seconds}秒, 可交易: {cooldown_info['can_trade']}")
        
        # 策略处理
        signals = self.strategy.on_market_event(event)
        
        # 记录信号
        if signals:
            if current_date not in self.debug_stats['signals_by_date']:
                self.debug_stats['signals_by_date'][current_date] = []
            
            for signal in signals:
                signal_info = {
                    'time': event.timestamp,
                    'type': signal.signal_type,
                    'reason': signal.reason,
                    'price': event.close
                }
                self.debug_stats['signals_by_date'][current_date].append(signal_info)
                
                self.logger.info(f"🎯 {current_date} {event.timestamp.strftime('%H:%M:%S')}: "
                               f"{signal.signal_type} 信号 - {signal.reason}")
                
                self.event_engine.put_event(signal)
        
        # 更新执行处理器的市场数据
        self.execution_handler.update_market_data(event)
        
        # 更新投资组合市值
        self.portfolio_manager.update_market_value(event)
    
    def _on_signal_event(self, event: SignalEvent):
        """处理交易信号事件"""
        orders = self.execution_handler.process_signal(event)
        for order in orders:
            self.event_engine.put_event(order)
    
    def _on_order_event(self, event: OrderEvent):
        """处理订单事件"""
        fill_event = self.execution_handler.execute_order(event)
        if fill_event:
            self.event_engine.put_event(fill_event)
    
    def _on_fill_event(self, event: FillEvent):
        """处理成交事件"""
        # 记录交易
        trade_date = event.timestamp.strftime('%Y-%m-%d')
        if trade_date not in self.debug_stats['trades_by_date']:
            self.debug_stats['trades_by_date'][trade_date] = []
        
        trade_info = {
            'time': event.timestamp,
            'direction': event.direction,
            'quantity': event.quantity,
            'price': event.fill_price
        }
        self.debug_stats['trades_by_date'][trade_date].append(trade_info)
        
        self.logger.info(f"✅ {trade_date} {event.timestamp.strftime('%H:%M:%S')}: "
                        f"{event.direction} {event.quantity} @ ¥{event.fill_price:.2f}")
        
        # 记录交易时间
        self.debug_stats['last_trade_times'].append(event.timestamp)
        
        # 处理成交
        self.portfolio_manager.process_fill_event(event)
    
    def run_debug(self):
        """运行调试"""
        self.logger.info("开始调试同一天多笔交易问题...")
        
        # 启动事件引擎
        self.event_engine.start()
        
        try:
            event_count = 0
            for market_event in self.data_provider.get_data_stream():
                self.event_engine.put_event(market_event)
                event_count += 1
                
                # 每5个事件打印一次进度
                if event_count % 5 == 0:
                    self.logger.info(f"已处理 {event_count} 个市场事件")
            
            # 等待所有事件处理完成
            self.event_engine.wait_for_completion(timeout=10)
            
            # 分析结果
            self._analyze_results()
            
        finally:
            self.event_engine.stop()
    
    def _analyze_results(self):
        """分析结果"""
        print("\n" + "=" * 80)
        print("同一天多笔交易问题分析")
        print("=" * 80)
        
        # 按日期统计信号
        print(f"\n📊 信号统计 (按日期):")
        for date, signals in self.debug_stats['signals_by_date'].items():
            print(f"  {date}: {len(signals)} 个信号")
            for i, signal in enumerate(signals, 1):
                print(f"    {i}. {signal['time'].strftime('%H:%M:%S')} - {signal['type']} ({signal['reason']})")
        
        # 按日期统计交易
        print(f"\n💼 交易统计 (按日期):")
        for date, trades in self.debug_stats['trades_by_date'].items():
            print(f"  {date}: {len(trades)} 笔交易")
            for i, trade in enumerate(trades, 1):
                print(f"    {i}. {trade['time'].strftime('%H:%M:%S')} - {trade['direction']} {trade['quantity']} @ ¥{trade['price']:.2f}")
        
        # 冷却期检查分析
        print(f"\n⏰ 冷却期检查分析:")
        same_day_trades = 0
        for check in self.debug_stats['cooldown_checks']:
            if not check['can_trade']:
                same_day_trades += 1
                print(f"  ❌ {check['current_time'].strftime('%Y-%m-%d %H:%M:%S')}: "
                      f"距离上次交易仅 {check['time_diff_seconds']:.0f} 秒，应该被冷却期阻止")
        
        if same_day_trades == 0:
            print("  ✅ 所有交易都通过了冷却期检查")
        else:
            print(f"  ❌ 发现 {same_day_trades} 次违反冷却期的交易")
        
        # 问题诊断
        print(f"\n🔍 问题诊断:")
        total_signals = sum(len(signals) for signals in self.debug_stats['signals_by_date'].values())
        total_trades = sum(len(trades) for trades in self.debug_stats['trades_by_date'].values())
        
        if len(self.debug_stats['signals_by_date']) == 1:
            print("  ❌ 所有信号都集中在同一天")
        
        if len(self.debug_stats['trades_by_date']) == 1:
            print("  ❌ 所有交易都集中在同一天")
        
        if total_signals > 1 and len(self.debug_stats['signals_by_date']) == 1:
            print("  🔍 可能原因: 冷却期机制未生效，或者信号生成逻辑有问题")
        
        print("=" * 80)

def main():
    """主函数"""
    print("同一天多笔交易问题调试器")
    print("分析为什么所有交易都集中在2025-06-23")
    print("-" * 50)
    
    debugger = SameDayTradingDebugger()
    debugger.run_debug()

if __name__ == "__main__":
    main()
