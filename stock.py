import akshare as ak
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# --------------------------
# 数据获取函数
# --------------------------
def get_stock_data(stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    从 AKShare 获取股票历史数据
    :param stock_code: 股票代码 (如 "600000")
    :param start_date: 开始日期 (格式 "YYYYMMDD")
    :param end_date: 结束日期 (格式 "YYYYMMDD")
    :return: 包含 OHLCV 数据的 DataFrame
    """
    try:
        # 获取后复权数据（包含分红送股）
        df = ak.stock_zh_a_hist(
            symbol=stock_code, 
            period="daily", 
            start_date=start_date, 
            end_date=end_date, 
            # adjust="qfq"
        )
        
        # 规范列名
        df = df.rename(columns={
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume'
        })
        
        # 转换日期格式
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # 计算平均成交量（20日移动平均）
        df['avg_vol'] = df['volume'].rolling(window=20, min_periods=1).mean()
        
        return df[['date', 'open', 'high', 'low', 'close', 'volume', 'avg_vol']]
    
    except Exception as e:
        raise RuntimeError(f"获取 {stock_code} 数据失败: {str(e)}")
    

def fetch_latest_kline(stock_code):
    """
    获取最新的K线数据（快照）。
    
    :return: 一个包含K线数据的字典，如果获取失败则返回 None。
    """
    try:
        # 使用 stock_bid_ask_em 获取实时行情
        stock_df = ak.stock_bid_ask_em(symbol=stock_code)

        if stock_df.empty:
            print(f"警告: 未找到股票代码 {stock_code} 的实时数据。")
            return None

        # 将 item 列设为索引，方便按名称取值
        stock_data = stock_df.set_index('item')['value']

        # 格式化为与 StockDataSimulator 兼容的字典
        kline = {
            'date': pd.to_datetime(datetime.now()),
            'open': float(stock_data['今开']),
            'high': float(stock_data['最高']),
            'low': float(stock_data['最低']),
            'close': float(stock_data['最新']),
            'volume': int(stock_data['总手']),
            'avg_vol': 0  # 实时数据中无此项，可留空或后续计算
        }
        
        return kline
    except Exception as e:
        print(f"获取 {stock_code} 实时数据时出错: {e}")
        raise e
        return None
    


