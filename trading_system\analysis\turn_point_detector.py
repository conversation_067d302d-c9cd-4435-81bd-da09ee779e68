"""
转折点检测器

基于价格行为和量价分析检测市场转折点（波峰和波谷）。
状态化设计，适配流式数据处理。

增强功能：
1. 量价确认：上升下降趋势成交量增加，量价背离信号
2. 识别高成交量价格区域（支撑/阻力区）
3. 转折点成交量验证趋势可靠性
4. 支持阻力位信号
"""

from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import logging
from collections import deque
import statistics

from ..core.event import MarketEvent, TurnPointEvent


@dataclass
class VolumeProfile:
    """成交量分布"""
    price_level: float
    volume: float
    count: int

    @property
    def avg_volume(self) -> float:
        return self.volume / self.count if self.count > 0 else 0.0


@dataclass
class SupportResistanceLevel:
    """支撑阻力位"""
    price: float
    strength: float  # 强度评分
    volume: float    # 该价位累计成交量
    touch_count: int # 触及次数
    level_type: str  # 'SUPPORT' or 'RESISTANCE'
    last_touch: datetime


@dataclass
class VolumeSignal:
    """量价信号"""
    signal_type: str  # 'VOLUME_CONFIRM', 'VOLUME_DIVERGENCE', 'HIGH_VOLUME_BREAKOUT'
    strength: float   # 信号强度 0-1
    description: str  # 信号描述


@dataclass
class CandidatePoint:
    """候选转折点"""
    timestamp: datetime
    price: float
    point_type: str  # 'PEAK' or 'TROUGH'
    start_date: datetime
    keep_days: int = 0
    cumulative_volume: float = 0.0
    volume_signals: List[VolumeSignal] = field(default_factory=list)
    support_resistance_level: Optional[SupportResistanceLevel] = None


class TurnPointDetector:
    """
    增强转折点检测器

    基于价格行为和量价分析检测波峰和波谷，支持流式数据处理。

    增强功能：
    - 量价确认分析
    - 支撑阻力位识别
    - 成交量背离检测
    - 高成交量突破信号
    """

    def __init__(self,
                 min_keep_days: int = 1,  # 降低最小持续天数，实现更快确认
                 min_extent_percent: float = 0.5,  # 降低最小变化幅度，提高敏感度
                 volume_window: int = 20,
                 volume_threshold: float = 1.5,
                 sr_sensitivity: float = 0.02,
                 realtime_mode: bool = True):  # 新增实时模式
        """
        初始化转折点检测器

        Args:
            min_keep_days: 最小持续天数
            min_extent_percent: 最小变化幅度百分比
            volume_window: 成交量分析窗口期
            volume_threshold: 成交量异常阈值（倍数）
            sr_sensitivity: 支撑阻力位敏感度（价格相似度阈值）
            realtime_mode: 实时模式，启用更快的转折点确认
        """
        self.min_keep_days = min_keep_days
        self.min_extent = min_extent_percent / 100.0
        self.volume_window = volume_window
        self.volume_threshold = volume_threshold
        self.sr_sensitivity = sr_sensitivity
        self.realtime_mode = realtime_mode
        self.logger = logging.getLogger(__name__)

        # 状态变量
        self.reset_state()
    
    def reset_state(self):
        """重置检测器状态"""
        self.candidate: Optional[CandidatePoint] = None
        self.last_trend_type: Optional[str] = None
        self.confirmed_points: List[TurnPointEvent] = []
        self.last_market_event: Optional[MarketEvent] = None

        # 量价分析相关状态
        self.volume_history: deque = deque(maxlen=self.volume_window)
        self.price_history: deque = deque(maxlen=self.volume_window)
        self.volume_profiles: List[VolumeProfile] = []
        self.support_resistance_levels: List[SupportResistanceLevel] = []

        # 趋势分析状态
        self.trend_volume_sum = 0.0  # 当前趋势累计成交量
        self.trend_start_volume = 0.0  # 趋势开始时的成交量

        # 实时确认相关状态
        self.pending_confirmations = []  # 待确认的转折点
        self.confirmation_buffer = deque(maxlen=5)  # 确认缓冲区
        
    def process_market_event(self, market_event: MarketEvent) -> Optional[TurnPointEvent]:
        """
        处理市场数据事件，检测转折点

        Args:
            market_event: 市场数据事件

        Returns:
            Optional[TurnPointEvent]: 如果检测到转折点则返回事件，否则返回None
        """
        # 更新历史数据
        self._update_history(market_event)

        if self.last_market_event is None:
            self.last_market_event = market_event
            return None

        # 计算当前趋势状态
        current_trend = self._determine_trend(self.last_market_event, market_event)

        # 更新成交量分布和支撑阻力位
        self._update_volume_profile(market_event)
        self._update_support_resistance(market_event)

        # 处理转折点逻辑
        turn_point_event = None

        if self.candidate is None:
            # 没有候选点，创建新的候选点
            self._create_candidate(market_event, current_trend)
        else:
            # 有候选点，检查是否需要更新或确认
            if current_trend == self.candidate.point_type:
                # 趋势延续，更新候选点
                self._update_candidate(market_event)

                # 实时模式：检查是否可以提前确认
                if self.realtime_mode:
                    turn_point_event = self._check_realtime_confirmation(market_event)
            else:
                # 趋势改变，确认当前候选点并创建新候选点
                turn_point_event = self._confirm_candidate()
                self._create_candidate(market_event, current_trend)

        # 处理待确认的转折点
        if self.realtime_mode:
            confirmed_event = self._process_pending_confirmations(market_event)
            if confirmed_event and turn_point_event is None:
                turn_point_event = confirmed_event

        self.last_trend_type = current_trend
        self.last_market_event = market_event

        return turn_point_event

    def _check_realtime_confirmation(self, market_event: MarketEvent) -> Optional[TurnPointEvent]:
        """
        实时模式：检查是否可以提前确认转折点

        Args:
            market_event: 当前市场事件

        Returns:
            Optional[TurnPointEvent]: 如果满足实时确认条件则返回转折点事件
        """
        if self.candidate is None:
            return None

        # 检查是否满足基本确认条件（放宽要求）
        if not self._validate_realtime_candidate(market_event):
            return None

        # 检查是否有足够的反向确认信号
        if self._has_reversal_confirmation(market_event):
            # 创建待确认的转折点
            pending_event = TurnPointEvent(
                symbol=market_event.symbol,
                timestamp=self.candidate.timestamp,
                point_type=self.candidate.point_type,
                price=self.candidate.price,
                start_date=self.candidate.start_date,
                keep_days=self.candidate.keep_days,
                extent_percent=self._calculate_extent_percent(),
                volume=self.candidate.cumulative_volume
            )

            # 添加到待确认列表
            self.pending_confirmations.append({
                'event': pending_event,
                'confirmation_date': market_event.timestamp,
                'confirmation_price': market_event.close
            })

            self.logger.info(f"Realtime candidate detected: {pending_event.point_type} at {pending_event.price:.2f} "
                           f"(will confirm after validation)")

            # 重置候选点，开始寻找下一个
            self.candidate = None

        return None

    def _validate_realtime_candidate(self, market_event: MarketEvent) -> bool:
        """
        验证实时候选点是否满足确认条件（放宽版本）

        Args:
            market_event: 当前市场事件

        Returns:
            bool: 是否满足实时确认条件
        """
        if self.candidate is None:
            return False

        # 放宽持续时间要求（至少1天）
        if self.candidate.keep_days < 1:
            return False

        # 放宽变化幅度要求（至少0.3%）
        extent_percent = self._calculate_extent_percent()
        if abs(extent_percent) < 0.003:  # 0.3%
            return False

        return True

    def _has_reversal_confirmation(self, market_event: MarketEvent) -> bool:
        """
        检查是否有反向确认信号

        Args:
            market_event: 当前市场事件

        Returns:
            bool: 是否有反向确认
        """
        if self.candidate is None:
            return False

        current_price = market_event.close
        candidate_price = self.candidate.price

        # 检查价格是否开始反向移动
        if self.candidate.point_type == 'PEAK':
            # 对于波峰，检查价格是否开始下跌
            price_decline = (candidate_price - current_price) / candidate_price
            return price_decline >= 0.01  # 至少下跌1%
        else:  # TROUGH
            # 对于波谷，检查价格是否开始上涨
            price_rise = (current_price - candidate_price) / candidate_price
            return price_rise >= 0.01  # 至少上涨1%

    def _process_pending_confirmations(self, market_event: MarketEvent) -> Optional[TurnPointEvent]:
        """
        处理待确认的转折点

        Args:
            market_event: 当前市场事件

        Returns:
            Optional[TurnPointEvent]: 确认的转折点事件
        """
        if not self.pending_confirmations:
            return None

        current_time = market_event.timestamp
        confirmed_events = []

        # 检查每个待确认的转折点
        for pending in self.pending_confirmations[:]:
            confirmation_days = (current_time - pending['confirmation_date']).days

            # 如果已经等待了足够长时间（2天），确认转折点
            if confirmation_days >= 2:
                event = pending['event']
                self.confirmed_points.append(event)
                confirmed_events.append(event)
                self.pending_confirmations.remove(pending)

                self.logger.info(f"Confirmed turn point (realtime): {event.point_type} at {event.price:.2f}")

            # 如果价格重新突破转折点，取消确认
            elif self._is_confirmation_invalidated(pending, market_event):
                self.logger.info(f"Cancelled turn point confirmation: {pending['event'].point_type} at {pending['event'].price:.2f}")
                self.pending_confirmations.remove(pending)

        # 返回第一个确认的事件
        return confirmed_events[0] if confirmed_events else None

    def _is_confirmation_invalidated(self, pending: Dict, market_event: MarketEvent) -> bool:
        """
        检查确认是否被无效化

        Args:
            pending: 待确认的转折点信息
            market_event: 当前市场事件

        Returns:
            bool: 确认是否被无效化
        """
        event = pending['event']
        current_price = market_event.close

        if event.point_type == 'PEAK':
            # 如果价格重新突破波峰，取消确认
            return current_price > event.price * 1.005  # 突破0.5%
        else:  # TROUGH
            # 如果价格重新跌破波谷，取消确认
            return current_price < event.price * 0.995  # 跌破0.5%

    def _update_history(self, market_event: MarketEvent):
        """更新历史数据"""
        self.volume_history.append(market_event.volume)
        self.price_history.append(market_event.close)

    def _update_volume_profile(self, market_event: MarketEvent):
        """更新成交量分布"""
        price_level = round(market_event.close, 2)

        # 查找现有价格水平
        existing_profile = None
        for profile in self.volume_profiles:
            if abs(profile.price_level - price_level) < 0.01:
                existing_profile = profile
                break

        if existing_profile:
            existing_profile.volume += market_event.volume
            existing_profile.count += 1
        else:
            self.volume_profiles.append(VolumeProfile(
                price_level=price_level,
                volume=market_event.volume,
                count=1
            ))

        # 保持合理的数据量
        if len(self.volume_profiles) > 1000:
            self.volume_profiles.sort(key=lambda x: x.volume, reverse=True)
            self.volume_profiles = self.volume_profiles[:500]

    def _update_support_resistance(self, market_event: MarketEvent):
        """更新支撑阻力位"""
        current_price = market_event.close

        # 检查是否触及现有支撑阻力位
        for level in self.support_resistance_levels:
            if abs(current_price - level.price) / level.price <= self.sr_sensitivity:
                level.touch_count += 1
                level.volume += market_event.volume
                level.last_touch = market_event.timestamp
                level.strength = min(level.strength + 0.1, 1.0)

        # 基于成交量分布识别新的支撑阻力位
        if len(self.volume_profiles) >= 10:
            high_volume_levels = sorted(self.volume_profiles, key=lambda x: x.volume, reverse=True)[:5]

            for profile in high_volume_levels:
                # 检查是否已存在相似价位
                exists = any(abs(level.price - profile.price_level) / profile.price_level <= self.sr_sensitivity
                           for level in self.support_resistance_levels)

                if not exists and profile.volume > self._get_avg_volume() * 2:
                    level_type = 'RESISTANCE' if profile.price_level > current_price else 'SUPPORT'
                    self.support_resistance_levels.append(SupportResistanceLevel(
                        price=profile.price_level,
                        strength=min(profile.volume / (self._get_avg_volume() * 10), 1.0),
                        volume=profile.volume,
                        touch_count=1,
                        level_type=level_type,
                        last_touch=market_event.timestamp
                    ))

        # 清理过期的支撑阻力位
        current_time = market_event.timestamp
        self.support_resistance_levels = [
            level for level in self.support_resistance_levels
            if (current_time - level.last_touch).days <= 30 or level.touch_count >= 3
        ]

    def _determine_trend(self, prev_event: MarketEvent, curr_event: MarketEvent) -> Optional[str]:
        """
        确定当前趋势类型
        
        Args:
            prev_event: 前一个市场事件
            curr_event: 当前市场事件
            
        Returns:
            Optional[str]: 趋势类型 ('PEAK', 'TROUGH', None)
        """
        # 计算状态值
        status = 0
        if curr_event.high >= prev_event.high:
            status += 1
        if curr_event.low <= prev_event.low:
            status += 2
        
        # 确定趋势类型
        if status == 1:
            return 'PEAK'
        elif status == 2:
            return 'TROUGH'
        else:
            # 状态不明确，保持上一个趋势
            return self.last_trend_type
    
    def _create_candidate(self, market_event: MarketEvent, trend_type: Optional[str]):
        """
        创建新的候选转折点

        Args:
            market_event: 市场数据事件
            trend_type: 趋势类型
        """
        if trend_type is None:
            return

        # 确定候选点价格
        if trend_type == 'PEAK':
            price = market_event.high
        else:  # TROUGH
            price = market_event.low

        # 分析量价信号
        volume_signals = self._analyze_volume_signals(market_event, trend_type)

        # 检查是否在支撑阻力位附近
        sr_level = self._find_nearby_support_resistance(price)

        self.candidate = CandidatePoint(
            timestamp=market_event.timestamp,
            price=price,
            point_type=trend_type,
            start_date=market_event.timestamp,
            keep_days=1,
            cumulative_volume=market_event.volume,
            volume_signals=volume_signals,
            support_resistance_level=sr_level
        )

        # 重置趋势成交量统计
        self.trend_volume_sum = market_event.volume
        self.trend_start_volume = market_event.volume
    
    def _update_candidate(self, market_event: MarketEvent):
        """
        更新候选转折点

        Args:
            market_event: 市场数据事件
        """
        if self.candidate is None:
            return

        # 更新价格（取极值）
        price_updated = False
        if self.candidate.point_type == 'PEAK':
            if market_event.high > self.candidate.price:
                self.candidate.price = market_event.high
                self.candidate.timestamp = market_event.timestamp
                price_updated = True
        else:  # TROUGH
            if market_event.low < self.candidate.price:
                self.candidate.price = market_event.low
                self.candidate.timestamp = market_event.timestamp
                price_updated = True

        # 更新持续天数和成交量
        days_diff = (market_event.timestamp - self.candidate.start_date).days
        self.candidate.keep_days = max(1, days_diff)
        self.candidate.cumulative_volume += market_event.volume
        self.trend_volume_sum += market_event.volume

        # 如果价格更新，重新分析量价信号
        if price_updated:
            new_signals = self._analyze_volume_signals(market_event, self.candidate.point_type)
            self.candidate.volume_signals.extend(new_signals)

            # 更新支撑阻力位信息
            sr_level = self._find_nearby_support_resistance(self.candidate.price)
            if sr_level:
                self.candidate.support_resistance_level = sr_level
    
    def _confirm_candidate(self) -> Optional[TurnPointEvent]:
        """
        确认候选转折点
        
        Returns:
            Optional[TurnPointEvent]: 确认的转折点事件
        """
        if self.candidate is None:
            return None
        
        # 检查是否满足确认条件
        if not self._validate_candidate():
            return None
        
        # 创建转折点事件
        turn_point_event = TurnPointEvent(
            symbol=self.last_market_event.symbol if self.last_market_event else "",
            timestamp=self.candidate.timestamp,
            point_type=self.candidate.point_type,
            price=self.candidate.price,
            start_date=self.candidate.start_date,
            keep_days=self.candidate.keep_days,
            extent_percent=self._calculate_extent_percent(),
            volume=self.candidate.cumulative_volume
        )
        
        # 保存确认的转折点
        self.confirmed_points.append(turn_point_event)
        
        self.logger.info(f"Confirmed turn point: {turn_point_event.point_type} at {turn_point_event.price:.2f}")
        
        return turn_point_event
    
    def _validate_candidate(self) -> bool:
        """
        验证候选点是否满足确认条件

        Returns:
            bool: 是否满足条件
        """
        if self.candidate is None:
            return False

        # 实时模式下放宽确认条件
        if self.realtime_mode:
            # 检查持续时间（实时模式下要求更低）
            if self.candidate.keep_days < max(1, self.min_keep_days // 2):
                return False

            # 检查变化幅度（实时模式下要求更低）
            extent_percent = self._calculate_extent_percent()
            if abs(extent_percent) < self.min_extent * 0.5:
                return False
        else:
            # 传统模式下的严格要求
            # 检查持续时间
            if self.candidate.keep_days < self.min_keep_days:
                return False

            # 检查变化幅度
            extent_percent = self._calculate_extent_percent()
            if abs(extent_percent) < self.min_extent:
                return False

        # 量价验证（实时模式下更宽松）
        volume_confirmation = self._validate_volume_confirmation()
        if not volume_confirmation and not self.realtime_mode:
            self.logger.debug(f"Volume confirmation failed for candidate at {self.candidate.price}")
            return False

        return True

    def _validate_volume_confirmation(self) -> bool:
        """
        验证成交量确认

        Returns:
            bool: 成交量是否确认趋势
        """
        if self.candidate is None or len(self.volume_history) < 5:
            return True  # 数据不足时不进行量价验证

        avg_volume = self._get_avg_volume()
        if avg_volume == 0:
            return True

        # 计算趋势期间平均成交量
        trend_avg_volume = self.trend_volume_sum / max(self.candidate.keep_days, 1)

        # 检查量价配合
        volume_ratio = trend_avg_volume / avg_volume

        # 实时模式下放宽量价验证要求
        if self.realtime_mode:
            # 对于重要的转折点，要求成交量放大（降低要求）
            if abs(self._calculate_extent_percent()) > self.min_extent * 2:
                return volume_ratio >= self.volume_threshold * 0.5  # 降低到50%

            # 检查是否存在量价背离信号
            divergence_signals = [s for s in self.candidate.volume_signals if s.signal_type == 'VOLUME_DIVERGENCE']
            if divergence_signals and any(s.strength > 0.5 for s in divergence_signals):
                return True  # 量价背离也是有效信号（降低强度要求）

            return volume_ratio >= 0.7  # 实时模式下允许适度缩量
        else:
            # 传统模式下的严格要求
            # 对于重要的转折点，要求成交量放大
            if abs(self._calculate_extent_percent()) > self.min_extent * 2:
                return volume_ratio >= self.volume_threshold * 0.8

            # 检查是否存在量价背离信号
            divergence_signals = [s for s in self.candidate.volume_signals if s.signal_type == 'VOLUME_DIVERGENCE']
            if divergence_signals and any(s.strength > 0.7 for s in divergence_signals):
                return True  # 量价背离也是有效信号

            return volume_ratio >= 1.0  # 至少不能缩量
    
    def _calculate_extent_percent(self) -> float:
        """
        计算变化幅度百分比

        Returns:
            float: 变化幅度百分比
        """
        if self.candidate is None:
            return 0.0

        # 如果没有确认的转折点，使用历史价格计算变化幅度
        if len(self.confirmed_points) == 0:
            if len(self.price_history) >= 2:
                # 使用价格历史的最低点和最高点计算
                min_price = min(self.price_history)
                max_price = max(self.price_history)
                if self.candidate.point_type == 'PEAK':
                    return (self.candidate.price - min_price) / min_price if min_price > 0 else 0.0
                else:  # TROUGH
                    return (max_price - self.candidate.price) / max_price if max_price > 0 else 0.0
            return 0.1  # 默认给一个小的变化幅度，确保第一个转折点可以被确认

        # 找到最近的相反类型转折点
        last_opposite_point = None
        for point in reversed(self.confirmed_points):
            if point.point_type != self.candidate.point_type:
                last_opposite_point = point
                break

        if last_opposite_point is None:
            # 如果没有相反类型的转折点，使用最近的任意转折点
            if len(self.confirmed_points) > 0:
                last_point = self.confirmed_points[-1]
                extent = (self.candidate.price - last_point.price) / last_point.price
                return abs(extent)
            return 0.1  # 默认变化幅度

        # 计算变化幅度
        extent = (self.candidate.price - last_opposite_point.price) / last_opposite_point.price
        return abs(extent)

    def _analyze_volume_signals(self, market_event: MarketEvent, trend_type: str) -> List[VolumeSignal]:
        """
        分析量价信号

        Args:
            market_event: 市场数据事件
            trend_type: 趋势类型

        Returns:
            List[VolumeSignal]: 量价信号列表
        """
        signals = []

        if len(self.volume_history) < 5:
            return signals

        current_volume = market_event.volume
        avg_volume = self._get_avg_volume()

        if avg_volume == 0:
            return signals

        volume_ratio = current_volume / avg_volume

        # 1. 成交量确认信号
        if volume_ratio >= self.volume_threshold:
            if trend_type == 'PEAK' and market_event.close > market_event.open:
                signals.append(VolumeSignal(
                    signal_type='VOLUME_CONFIRM',
                    strength=min(volume_ratio / self.volume_threshold, 1.0),
                    description=f'上涨趋势成交量放大确认 (量比: {volume_ratio:.2f})'
                ))
            elif trend_type == 'TROUGH' and market_event.close < market_event.open:
                signals.append(VolumeSignal(
                    signal_type='VOLUME_CONFIRM',
                    strength=min(volume_ratio / self.volume_threshold, 1.0),
                    description=f'下跌趋势成交量放大确认 (量比: {volume_ratio:.2f})'
                ))

        # 2. 量价背离信号
        if len(self.price_history) >= 5 and len(self.volume_history) >= 5:
            price_trend = self._calculate_price_trend()
            volume_trend = self._calculate_volume_trend()

            # 价格上涨但成交量下降，或价格下跌但成交量下降
            if (price_trend > 0 and volume_trend < -0.1) or (price_trend < 0 and volume_trend < -0.1):
                signals.append(VolumeSignal(
                    signal_type='VOLUME_DIVERGENCE',
                    strength=abs(price_trend - volume_trend) / 2,
                    description=f'量价背离信号 (价格趋势: {price_trend:.2f}, 成交量趋势: {volume_trend:.2f})'
                ))

        # 3. 高成交量突破信号
        if volume_ratio >= self.volume_threshold * 1.5:
            # 检查是否突破支撑阻力位
            sr_level = self._find_nearby_support_resistance(market_event.close)
            if sr_level:
                signals.append(VolumeSignal(
                    signal_type='HIGH_VOLUME_BREAKOUT',
                    strength=min(volume_ratio / (self.volume_threshold * 1.5), 1.0),
                    description=f'高成交量突破{sr_level.level_type}位 {sr_level.price:.2f} (量比: {volume_ratio:.2f})'
                ))

        return signals

    def _calculate_price_trend(self) -> float:
        """计算价格趋势（最近5个周期的斜率）"""
        if len(self.price_history) < 5:
            return 0.0

        recent_prices = list(self.price_history)[-5:]
        x = list(range(len(recent_prices)))

        # 简单线性回归计算斜率
        n = len(recent_prices)
        sum_x = sum(x)
        sum_y = sum(recent_prices)
        sum_xy = sum(x[i] * recent_prices[i] for i in range(n))
        sum_x2 = sum(xi * xi for xi in x)

        if n * sum_x2 - sum_x * sum_x == 0:
            return 0.0

        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        return slope / recent_prices[0] if recent_prices[0] != 0 else 0.0  # 标准化

    def _calculate_volume_trend(self) -> float:
        """计算成交量趋势（最近5个周期的斜率）"""
        if len(self.volume_history) < 5:
            return 0.0

        recent_volumes = list(self.volume_history)[-5:]
        x = list(range(len(recent_volumes)))

        # 简单线性回归计算斜率
        n = len(recent_volumes)
        sum_x = sum(x)
        sum_y = sum(recent_volumes)
        sum_xy = sum(x[i] * recent_volumes[i] for i in range(n))
        sum_x2 = sum(xi * xi for xi in x)

        if n * sum_x2 - sum_x * sum_x == 0:
            return 0.0

        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        return slope / recent_volumes[0] if recent_volumes[0] != 0 else 0.0  # 标准化

    def _find_nearby_support_resistance(self, price: float) -> Optional[SupportResistanceLevel]:
        """查找附近的支撑阻力位"""
        for level in self.support_resistance_levels:
            if abs(price - level.price) / level.price <= self.sr_sensitivity:
                return level
        return None

    def _get_avg_volume(self) -> float:
        """获取平均成交量"""
        if len(self.volume_history) == 0:
            return 0.0
        return statistics.mean(self.volume_history)

    def get_confirmed_points(self) -> List[TurnPointEvent]:
        """
        获取所有确认的转折点
        
        Returns:
            List[TurnPointEvent]: 转折点列表
        """
        return self.confirmed_points.copy()
    
    def get_current_candidate(self) -> Optional[CandidatePoint]:
        """
        获取当前候选点

        Returns:
            Optional[CandidatePoint]: 当前候选点
        """
        return self.candidate

    def get_support_resistance_levels(self) -> List[SupportResistanceLevel]:
        """
        获取当前支撑阻力位

        Returns:
            List[SupportResistanceLevel]: 支撑阻力位列表
        """
        return sorted(self.support_resistance_levels, key=lambda x: x.strength, reverse=True)

    def get_volume_profile(self) -> List[VolumeProfile]:
        """
        获取成交量分布

        Returns:
            List[VolumeProfile]: 成交量分布列表
        """
        return sorted(self.volume_profiles, key=lambda x: x.volume, reverse=True)

    def get_current_volume_signals(self) -> List[VolumeSignal]:
        """
        获取当前候选点的量价信号

        Returns:
            List[VolumeSignal]: 量价信号列表
        """
        if self.candidate:
            return self.candidate.volume_signals
        return []

    def get_market_analysis(self) -> Dict[str, Any]:
        """
        获取市场分析报告

        Returns:
            Dict[str, Any]: 包含各种分析结果的字典
        """
        analysis = {
            'current_candidate': None,
            'support_resistance_levels': [],
            'volume_signals': [],
            'volume_analysis': {},
            'trend_analysis': {}
        }

        if self.candidate:
            analysis['current_candidate'] = {
                'timestamp': self.candidate.timestamp,
                'price': self.candidate.price,
                'point_type': self.candidate.point_type,
                'keep_days': self.candidate.keep_days,
                'cumulative_volume': self.candidate.cumulative_volume,
                'volume_signals': [
                    {
                        'type': signal.signal_type,
                        'strength': signal.strength,
                        'description': signal.description
                    } for signal in self.candidate.volume_signals
                ]
            }

        analysis['support_resistance_levels'] = [
            {
                'price': level.price,
                'strength': level.strength,
                'volume': level.volume,
                'touch_count': level.touch_count,
                'level_type': level.level_type,
                'last_touch': level.last_touch
            } for level in self.get_support_resistance_levels()[:10]  # 取前10个最强的
        ]

        if len(self.volume_history) > 0:
            analysis['volume_analysis'] = {
                'current_volume': self.volume_history[-1] if self.volume_history else 0,
                'avg_volume': self._get_avg_volume(),
                'volume_ratio': self.volume_history[-1] / self._get_avg_volume() if self._get_avg_volume() > 0 else 0,
                'trend_volume_sum': self.trend_volume_sum,
                'volume_trend': self._calculate_volume_trend()
            }

        if len(self.price_history) > 0:
            analysis['trend_analysis'] = {
                'current_price': self.price_history[-1] if self.price_history else 0,
                'price_trend': self._calculate_price_trend(),
                'last_trend_type': self.last_trend_type
            }

        return analysis
