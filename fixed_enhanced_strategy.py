#!/usr/bin/env python3
"""
修复版增强转折点策略

解决问题：
1. 同一天多笔交易 - 添加交易冷却期
2. 买卖不匹配 - 修复持仓状态管理
3. 价格异常 - 使用当前市场价格而非转折点价格
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from trading_system.strategies.base_strategy import BaseStrategy
from trading_system.core.event import MarketEvent, SignalEvent, TurnPointEvent
from trading_system.analysis.turn_point_detector import TurnPointDetector, VolumeSignal, SupportResistanceLevel


class FixedEnhancedTurnPointStrategy(BaseStrategy):
    """
    修复版增强转折点策略
    
    修复的问题：
    1. 交易频率控制 - 添加最小交易间隔
    2. 持仓状态管理 - 确保买卖匹配
    3. 价格合理性 - 使用市场价格而非信号价格
    4. 信号过滤 - 更严格的信号验证
    """
    
    def __init__(self, symbol: str):
        super().__init__("FixedEnhancedTurnPointStrategy", symbol)
        
        # 策略参数
        self.set_parameter('position_size', 100)
        self.set_parameter('min_volume_confirm_strength', 0.6)
        self.set_parameter('min_breakout_strength', 0.8)
        self.set_parameter('stop_loss_percent', 0.05)
        self.set_parameter('take_profit_percent', 0.10)
        self.set_parameter('max_position_hold_days', 15)
        self.set_parameter('min_trade_interval_days', 1)  # 最小交易间隔
        self.set_parameter('max_position_size', 1000)     # 最大持仓限制
        
        # 创建转折点检测器
        self.detector = TurnPointDetector(
            min_keep_days=3,
            min_extent_percent=3.0,  # 提高最小变化幅度要求
            volume_window=15,
            volume_threshold=1.5,
            sr_sensitivity=0.02
        )
        
        # 策略状态
        self.current_position = 0      # 当前持仓 (正数=多头, 负数=空头)
        self.entry_price = 0.0         # 入场价格
        self.entry_time = None         # 入场时间
        self.last_trade_time = None    # 最后交易时间
        self.last_turn_point = None    # 最后一个转折点
        
        # 信号统计
        self.signal_stats = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'volume_confirmed': 0,
            'sr_confirmed': 0,
            'filtered_out': 0,
            'cooldown_filtered': 0
        }
    
    def on_market_event(self, event: MarketEvent) -> List[SignalEvent]:
        """
        处理市场数据事件
        
        Args:
            event: 市场数据事件
            
        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
        signals = []
        
        # 更新转折点检测器
        turn_point = self.detector.process_market_event(event)
        
        # 检查止损止盈
        stop_signals = self._check_stop_conditions(event)
        signals.extend(stop_signals)
        
        # 如果检测到转折点，生成交易信号
        if turn_point:
            self.last_turn_point = turn_point
            turn_point_signals = self._process_turn_point(turn_point, event)
            signals.extend(turn_point_signals)
        
        # 检查持仓时间限制
        time_signals = self._check_time_limits(event)
        signals.extend(time_signals)
        
        return signals
    
    def _process_turn_point(self, turn_point: TurnPointEvent, market_event: MarketEvent) -> List[SignalEvent]:
        """
        处理转折点，生成交易信号
        
        Args:
            turn_point: 转折点事件
            market_event: 市场数据事件
            
        Returns:
            List[SignalEvent]: 交易信号列表
        """
        signals = []
        
        # 检查交易冷却期
        if not self._check_trade_cooldown(market_event.timestamp):
            self.signal_stats['cooldown_filtered'] += 1
            self.logger.debug(f"Signal filtered due to cooldown period")
            return signals
        
        # 获取当前市场分析
        analysis = self.detector.get_market_analysis()
        volume_signals = self.detector.get_current_volume_signals()
        sr_levels = self.detector.get_support_resistance_levels()
        
        # 信号过滤和确认
        signal_strength = self._calculate_signal_strength(turn_point, volume_signals, sr_levels, analysis)
        
        if signal_strength < 0.7:  # 提高信号强度要求
            self.signal_stats['filtered_out'] += 1
            self.logger.debug(f"Signal filtered out: strength {signal_strength:.2f} too low")
            return signals
        
        position_size = self.get_parameter('position_size')
        
        # 生成交易信号 - 使用当前市场价格而非转折点价格
        current_price = market_event.close
        
        if turn_point.point_type == 'TROUGH' and self.current_position <= 0:
            # 谷底转折点 - 买入信号
            signal = self._create_buy_signal(current_price, market_event.timestamp, signal_strength, 
                                           position_size, volume_signals, sr_levels)
            if signal:
                signals.append(signal)
                self.signal_stats['buy_signals'] += 1
                
        elif turn_point.point_type == 'PEAK' and self.current_position >= 0:
            # 峰值转折点 - 卖出信号
            signal = self._create_sell_signal(current_price, market_event.timestamp, signal_strength,
                                            position_size, volume_signals, sr_levels)
            if signal:
                signals.append(signal)
                self.signal_stats['sell_signals'] += 1
        
        self.signal_stats['total_signals'] += len(signals)
        return signals
    
    def _check_trade_cooldown(self, current_time: datetime) -> bool:
        """
        检查交易冷却期
        
        Args:
            current_time: 当前时间
            
        Returns:
            bool: 是否可以交易
        """
        if self.last_trade_time is None:
            return True
        
        min_interval = self.get_parameter('min_trade_interval_days')
        time_diff = current_time - self.last_trade_time
        
        return time_diff.days >= min_interval
    
    def _calculate_signal_strength(self, turn_point: TurnPointEvent, 
                                 volume_signals: List[VolumeSignal],
                                 sr_levels: List[SupportResistanceLevel],
                                 analysis: Dict[str, Any]) -> float:
        """
        计算信号强度
        
        Args:
            turn_point: 转折点事件
            volume_signals: 量价信号列表
            sr_levels: 支撑阻力位列表
            analysis: 市场分析
            
        Returns:
            float: 信号强度 (0-1)
        """
        strength = 0.2  # 降低基础强度
        
        # 1. 转折点本身的强度
        if abs(turn_point.extent_percent) > 0.05:  # 5%以上的变化
            strength += 0.2
        if turn_point.keep_days >= 3:  # 持续3天以上
            strength += 0.1
        
        # 2. 量价信号确认
        volume_confirm_signals = [s for s in volume_signals if s.signal_type == 'VOLUME_CONFIRM']
        if volume_confirm_signals:
            max_volume_strength = max(s.strength for s in volume_confirm_signals)
            if max_volume_strength >= self.get_parameter('min_volume_confirm_strength'):
                strength += 0.3
                self.signal_stats['volume_confirmed'] += 1
        
        # 3. 高成交量突破信号
        breakout_signals = [s for s in volume_signals if s.signal_type == 'HIGH_VOLUME_BREAKOUT']
        if breakout_signals:
            max_breakout_strength = max(s.strength for s in breakout_signals)
            if max_breakout_strength >= self.get_parameter('min_breakout_strength'):
                strength += 0.3
        
        # 4. 支撑阻力位确认
        for level in sr_levels[:3]:  # 检查前3个最强的支撑阻力位
            price_diff = abs(turn_point.price - level.price) / level.price
            if price_diff <= 0.02:  # 在2%范围内
                strength += level.strength * 0.2
                self.signal_stats['sr_confirmed'] += 1
                break
        
        return min(strength, 1.0)  # 限制在1.0以内
    
    def _create_buy_signal(self, price: float, timestamp: datetime, strength: float, 
                          position_size: int, volume_signals: List[VolumeSignal],
                          sr_levels: List[SupportResistanceLevel]) -> Optional[SignalEvent]:
        """创建买入信号"""
        
        # 检查持仓限制
        if self.current_position >= self.get_parameter('max_position_size'):
            self.logger.debug("Cannot buy: position limit reached")
            return None
        
        # 构建信号原因
        reasons = [f"TROUGH转折点确认"]
        
        # 添加量价信号原因
        for signal in volume_signals:
            if signal.strength > 0.6:
                reasons.append(f"{signal.signal_type}({signal.strength:.2f})")
        
        reason = "; ".join(reasons)
        
        signal = self._create_signal(
            signal_type='BUY',
            quantity=position_size,
            price=price,
            reason=reason,
            confidence=strength
        )
        
        # 更新持仓状态
        self.current_position += position_size
        self.entry_price = price
        self.entry_time = timestamp
        self.last_trade_time = timestamp
        
        self.logger.info(f"BUY signal generated: {reason} @ {price:.2f} (strength: {strength:.2f})")
        return signal
    
    def _create_sell_signal(self, price: float, timestamp: datetime, strength: float,
                           position_size: int, volume_signals: List[VolumeSignal],
                           sr_levels: List[SupportResistanceLevel]) -> Optional[SignalEvent]:
        """创建卖出信号"""
        
        # 确定卖出数量
        if self.current_position > 0:
            sell_quantity = min(self.current_position, position_size)
        else:
            sell_quantity = position_size
        
        # 构建信号原因
        reasons = [f"PEAK转折点确认"]
        
        # 添加量价信号原因
        for signal in volume_signals:
            if signal.strength > 0.6:
                reasons.append(f"{signal.signal_type}({signal.strength:.2f})")
        
        reason = "; ".join(reasons)
        
        signal = self._create_signal(
            signal_type='SELL',
            quantity=sell_quantity,
            price=price,
            reason=reason,
            confidence=strength
        )
        
        # 更新持仓状态
        self.current_position -= sell_quantity
        if self.current_position <= 0:
            self.entry_price = 0.0
            self.entry_time = None
        self.last_trade_time = timestamp
        
        self.logger.info(f"SELL signal generated: {reason} @ {price:.2f} (strength: {strength:.2f})")
        return signal
    
    def _check_stop_conditions(self, event: MarketEvent) -> List[SignalEvent]:
        """检查止损止盈条件"""
        signals = []
        
        if self.current_position == 0 or self.entry_price == 0:
            return signals
        
        current_price = event.close
        stop_loss_pct = self.get_parameter('stop_loss_percent')
        take_profit_pct = self.get_parameter('take_profit_percent')
        
        if self.current_position > 0:  # 多头持仓
            # 止损检查
            if current_price <= self.entry_price * (1 - stop_loss_pct):
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=self.current_position,
                    price=current_price,
                    reason=f'止损 (入场: {self.entry_price:.2f}, 当前: {current_price:.2f})',
                    confidence=1.0
                )
                signals.append(signal)
                self._reset_position()
                self.last_trade_time = event.timestamp
            
            # 止盈检查
            elif current_price >= self.entry_price * (1 + take_profit_pct):
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=self.current_position,
                    price=current_price,
                    reason=f'止盈 (入场: {self.entry_price:.2f}, 当前: {current_price:.2f})',
                    confidence=1.0
                )
                signals.append(signal)
                self._reset_position()
                self.last_trade_time = event.timestamp
        
        return signals
    
    def _check_time_limits(self, event: MarketEvent) -> List[SignalEvent]:
        """检查持仓时间限制"""
        signals = []
        
        if self.current_position == 0 or self.entry_time is None:
            return signals
        
        max_hold_days = self.get_parameter('max_position_hold_days')
        hold_duration = event.timestamp - self.entry_time
        
        if hold_duration.days >= max_hold_days:
            # 超过最大持仓时间，强制平仓
            if self.current_position > 0:
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=self.current_position,
                    price=event.close,
                    reason=f'超时平仓 (持仓{hold_duration.days}天)',
                    confidence=0.8
                )
            else:
                signal = self._create_signal(
                    signal_type='BUY',
                    quantity=abs(self.current_position),
                    price=event.close,
                    reason=f'超时平仓 (持仓{hold_duration.days}天)',
                    confidence=0.8
                )
            
            signals.append(signal)
            self._reset_position()
            self.last_trade_time = event.timestamp
        
        return signals
    
    def _reset_position(self):
        """重置持仓状态"""
        self.current_position = 0
        self.entry_price = 0.0
        self.entry_time = None
    
    def reset(self):
        """重置策略状态"""
        super().reset()
        self.detector.reset_state()
        self._reset_position()
        self.last_trade_time = None
        self.last_turn_point = None
        self.signal_stats = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'volume_confirmed': 0,
            'sr_confirmed': 0,
            'filtered_out': 0,
            'cooldown_filtered': 0
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        base_stats = super().get_stats()
        
        base_stats.update({
            'current_position': self.current_position,
            'entry_price': self.entry_price,
            'entry_time': self.entry_time,
            'last_trade_time': self.last_trade_time,
            'signal_stats': self.signal_stats.copy(),
            'detector_stats': {
                'confirmed_points': len(self.detector.get_confirmed_points()),
                'sr_levels': len(self.detector.get_support_resistance_levels()),
                'volume_signals': len(self.detector.get_current_volume_signals())
            }
        })
        
        return base_stats
    
    def get_market_analysis(self) -> Dict[str, Any]:
        """获取当前市场分析"""
        return self.detector.get_market_analysis()

if __name__ == "__main__":
    print("修复版增强转折点策略已创建")
    print("主要修复:")
    print("1. 添加交易冷却期，避免同日多次交易")
    print("2. 修复持仓状态管理，确保买卖匹配")
    print("3. 使用当前市场价格，避免价格异常")
    print("4. 提高信号过滤标准，减少无效交易")
