# 量化交易系统

基于事件驱动架构的Python量化交易系统，支持股票回测和实盘交易。

## 系统特点

- **事件驱动**: 基于高效的事件驱动架构
- **模块化设计**: 松耦合的组件设计，易于扩展
- **完整回测**: 支持历史数据回测和性能分析
- **实盘就绪**: 可从回测无缝切换到实盘交易
- **流式处理**: 严格遵守"无未来数据"原则

## 快速开始

1. **安装依赖**

```bash
pip install -r requirements.txt
```

2. **运行示例回测**

```bash
python main.py
```

3. **查看回测结果**

回测完成后会自动生成绩效报告和交易图表。

## 文档导航

- [开发指南](docs/development_guide.md): 系统架构和开发流程
- [策略示例](docs/strategy_example.md): 详细的策略开发示例
- [API参考](docs/api_reference.md): 完整的API文档

## 目录结构

```
trading_system/
├── main.py                     # 系统入口
├── docs/                       # 文档
├── trading_system/            
│   ├── core/                  # 核心模块
│   ├── data/                  # 数据模块
│   ├── analysis/             # 分析模块
│   ├── strategies/           # 策略模块
│   ├── execution/            # 执行模块
│   ├── portfolio/            # 账户模块
│   └── utils/                # 工具模块
└── tests/                     # 测试用例
```

## 示例代码

```python
from trading_system import TradingSystem

# 配置系统
config = {
    'symbol': '003021',          # 交易标的
    'start_date': '20250101',    # 回测起始日期
    'end_date': '20250630',      # 回测结束日期
    'initial_cash': 100000       # 初始资金
}

# 运行回测
system = TradingSystem(config)
system.initialize()
system.run_backtest()
system.show_results()
```

## 开发新策略

1. 创建策略类

```python
from trading_system.strategies import BaseStrategy

class MyStrategy(BaseStrategy):
    def process_event(self, event):
        # 实现您的策略逻辑
        pass
```

2. 配置并运行

```python
system.strategy = MyStrategy(config['symbol'])
system.run_backtest()
```

## 贡献指南

1. Fork 本仓库
2. 创建特性分支
3. 提交您的修改
4. 推送到分支
5. 创建 Pull Request

## 待办事项

- [ ] 支持更多数据源
- [ ] 添加机器学习模块
- [ ] 实现并行回测
- [ ] 优化性能分析报告
- [ ] 添加实盘交易接口

## 许可证

MIT

## 联系方式

如有问题或建议，请提交 Issue 或 Pull Request。
