"""
回测执行处理器

模拟交易所的订单执行，用于历史数据回测。
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
import random

from .base_handler import BaseExecutionHandler, OrderValidator
from ..core.event import SignalEvent, OrderEvent, FillEvent, MarketEvent
from ..core.engine import EventEngine


class BacktestExecutionHandler(BaseExecutionHandler):
    """
    回测执行处理器
    
    模拟真实交易环境的订单执行，包括滑点、部分成交等情况。
    """
    
    def __init__(self, event_engine: EventEngine,
                 slippage_pct: float = 0.001,
                 partial_fill_prob: float = 0.1):
        """
        初始化回测执行处理器
        
        Args:
            event_engine: 事件引擎实例
            slippage_pct: 滑点百分比
            partial_fill_prob: 部分成交概率
        """
        super().__init__("BacktestHandler")
        
        self.event_engine = event_engine
        self.slippage_pct = slippage_pct
        self.partial_fill_prob = partial_fill_prob
        
        # 当前市场数据
        self.current_market_data: Optional[MarketEvent] = None
        
        # 订单簿（简化实现）
        self.pending_orders: Dict[str, OrderEvent] = {}
        
        # 执行历史
        self.execution_history: List[FillEvent] = []
    
    def update_market_data(self, market_event: MarketEvent):
        """
        更新当前市场数据
        
        Args:
            market_event: 市场数据事件
        """
        self.current_market_data = market_event
        
        # 检查待执行的限价单
        self._check_pending_orders()
    
    def process_signal(self, signal_event: SignalEvent) -> List[OrderEvent]:
        """
        处理交易信号，生成订单
        
        Args:
            signal_event: 交易信号事件
            
        Returns:
            List[OrderEvent]: 生成的订单列表
        """
        orders = []
        
        # 忽略HOLD信号
        if signal_event.signal_type == 'HOLD':
            return orders
        
        # 根据信号类型确定订单类型
        if signal_event.price is None:
            # 市价单
            order = self._create_order_from_signal(signal_event, 'MARKET')
        else:
            # 限价单
            order = self._create_order_from_signal(signal_event, 'LIMIT')
        
        # 验证订单
        is_valid, error_msg = OrderValidator.validate_order(order)
        if not is_valid:
            self._reject_order(order, error_msg)
            return orders
        
        orders.append(order)
        price_str = f"{order.price:.2f}" if order.price is not None else "MARKET"
        self.logger.info(f"Created order: {order.direction} {order.quantity} {order.symbol} @ {price_str}")
        
        return orders
    
    def execute_order(self, order_event: OrderEvent) -> Optional[FillEvent]:
        """
        执行订单
        
        Args:
            order_event: 订单事件
            
        Returns:
            Optional[FillEvent]: 成交事件
        """
        if self.current_market_data is None:
            self.logger.warning("No market data available for order execution")
            return None
        
        if order_event.order_type == 'MARKET':
            return self._execute_market_order(order_event)
        elif order_event.order_type == 'LIMIT':
            return self._execute_limit_order(order_event)
        else:
            self.logger.warning(f"Unsupported order type: {order_event.order_type}")
            return None
    
    def _execute_market_order(self, order_event: OrderEvent) -> Optional[FillEvent]:
        """
        执行市价单
        
        Args:
            order_event: 订单事件
            
        Returns:
            Optional[FillEvent]: 成交事件
        """
        if self.current_market_data is None:
            return None
        
        # 确定成交价格（考虑滑点）
        if order_event.direction == 'BUY':
            # 买单使用卖价（ask），这里简化为收盘价加滑点
            base_price = self.current_market_data.close
            fill_price = base_price * (1 + self.slippage_pct)
        else:  # SELL
            # 卖单使用买价（bid），这里简化为收盘价减滑点
            base_price = self.current_market_data.close
            fill_price = base_price * (1 - self.slippage_pct)
        
        # 确定成交数量（可能部分成交）
        fill_quantity = order_event.quantity
        if random.random() < self.partial_fill_prob:
            # 部分成交
            fill_quantity = int(order_event.quantity * random.uniform(0.5, 0.9))
            fill_quantity = max(1, fill_quantity)  # 至少成交1股
        
        # 创建成交事件
        fill_event = self._create_fill_from_order(order_event, fill_price, fill_quantity)
        self.execution_history.append(fill_event)
        
        self.logger.info(f"Market order filled: {fill_event.direction} {fill_event.quantity} "
                        f"{fill_event.symbol} @ {fill_event.fill_price:.2f}")
        
        return fill_event
    
    def _execute_limit_order(self, order_event: OrderEvent) -> Optional[FillEvent]:
        """
        执行限价单
        
        Args:
            order_event: 订单事件
            
        Returns:
            Optional[FillEvent]: 成交事件，如果不能立即成交则返回None
        """
        if self.current_market_data is None:
            return None
        
        current_price = self.current_market_data.close
        limit_price = order_event.price
        
        # 检查是否可以成交
        can_fill = False
        if order_event.direction == 'BUY' and current_price <= limit_price:
            can_fill = True
        elif order_event.direction == 'SELL' and current_price >= limit_price:
            can_fill = True
        
        if can_fill:
            # 限价单成交价格为限价
            fill_price = limit_price
            fill_quantity = order_event.quantity
            
            # 创建成交事件
            fill_event = self._create_fill_from_order(order_event, fill_price, fill_quantity)
            self.execution_history.append(fill_event)
            
            self.logger.info(f"Limit order filled: {fill_event.direction} {fill_event.quantity} "
                            f"{fill_event.symbol} @ {fill_event.fill_price:.2f}")
            
            return fill_event
        else:
            # 不能立即成交，加入待执行队列
            self.pending_orders[order_event.order_id] = order_event
            self.logger.info(f"Limit order pending: {order_event.direction} {order_event.quantity} "
                            f"{order_event.symbol} @ {order_event.price:.2f}")
            return None
    
    def _check_pending_orders(self):
        """
        检查待执行的限价单
        """
        if self.current_market_data is None:
            return
        
        current_price = self.current_market_data.close
        filled_orders = []
        
        for order_id, order in self.pending_orders.items():
            can_fill = False
            
            if order.direction == 'BUY' and current_price <= order.price:
                can_fill = True
            elif order.direction == 'SELL' and current_price >= order.price:
                can_fill = True
            
            if can_fill:
                # 执行订单
                fill_event = self._create_fill_from_order(order, order.price, order.quantity)
                self.execution_history.append(fill_event)
                filled_orders.append(order_id)
                
                # 将成交事件放入事件队列
                self.event_engine.put_event(fill_event)
                
                self.logger.info(f"Pending limit order filled and event dispatched: {fill_event.direction} "
                                f"{fill_event.quantity} {fill_event.symbol} @ {fill_event.fill_price:.2f}")
        
        # 移除已成交的订单
        if filled_orders:
            for order_id in filled_orders:
                if order_id in self.pending_orders:
                    del self.pending_orders[order_id]
    
    def cancel_order(self, order_id: str) -> bool:
        """
        取消订单
        
        Args:
            order_id: 订单ID
            
        Returns:
            bool: 是否成功取消
        """
        if order_id in self.pending_orders:
            del self.pending_orders[order_id]
            self.logger.info(f"Order {order_id} cancelled")
            return True
        return False
    
    def get_pending_orders(self) -> List[OrderEvent]:
        """
        获取待执行订单
        
        Returns:
            List[OrderEvent]: 待执行订单列表
        """
        return list(self.pending_orders.values())
    
    def get_execution_history(self) -> List[FillEvent]:
        """
        获取执行历史
        
        Returns:
            List[FillEvent]: 执行历史列表
        """
        return self.execution_history.copy()
    
    def reset(self):
        """重置处理器状态"""
        self.pending_orders.clear()
        self.execution_history.clear()
        self.current_market_data = None
        self.reset_stats()
        self.logger.info("Backtest execution handler reset")
