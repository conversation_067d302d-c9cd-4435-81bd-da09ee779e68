# 策略开发示例

本文档通过一个具体的策略实现示例，展示如何在交易系统中开发和测试策略。

## 1. 转折点策略示例

以下是一个基于价格转折点的交易策略实现示例。该策略通过识别价格的重要转折点来进行交易决策。

```python
from trading_system.strategies import BaseStrategy
from trading_system.core import MarketEvent, TurnPointEvent, SignalEvent

class TurnPointStrategy(BaseStrategy):
    """
    转折点交易策略
    
    策略逻辑：
    1. 在向上转折点确认后，产生买入信号
    2. 在向下转折点确认后，产生卖出信号
    3. 考虑持仓时间和波动幅度的限制
    """
    
    def __init__(self, symbol: str):
        super().__init__()
        self.symbol = symbol
        self.current_position = 0  # 当前持仓状态
        self.last_trade_price = 0  # 上次交易价格
        self.position_start_time = None  # 持仓开始时间
        
    def process_event(self, event):
        if isinstance(event, TurnPointEvent):
            return self._process_turn_point(event)
        return []
        
    def _process_turn_point(self, event):
        signals = []
        
        # 向上转折点，考虑买入
        if event.direction == "up" and self.current_position <= 0:
            signal = SignalEvent(
                symbol=self.symbol,
                timestamp=event.timestamp,
                signal_type="LONG",
                strength=1.0,
                price=event.price
            )
            signals.append(signal)
            
        # 向下转折点，考虑卖出
        elif event.direction == "down" and self.current_position >= 0:
            signal = SignalEvent(
                symbol=self.symbol,
                timestamp=event.timestamp,
                signal_type="SHORT",
                strength=1.0,
                price=event.price
            )
            signals.append(signal)
            
        return signals
```

## 2. 策略开发步骤

### 2.1 继承基类

所有策略都必须继承 `BaseStrategy`：

```python
from trading_system.strategies import BaseStrategy

class MyStrategy(BaseStrategy):
    def __init__(self, symbol: str):
        super().__init__()
        self.symbol = symbol
```

### 2.2 定义状态变量

根据策略需求定义必要的状态变量：

```python
def __init__(self, symbol: str):
    super().__init__()
    self.symbol = symbol
    
    # 策略状态变量
    self.current_position = 0
    self.last_trade_price = 0
    self.position_start_time = None
```

### 2.3 实现事件处理

处理相关的事件类型：

```python
def process_event(self, event):
    if isinstance(event, MarketEvent):
        return self._process_market_data(event)
    elif isinstance(event, TurnPointEvent):
        return self._process_turn_point(event)
    return []
```

### 2.4 生成交易信号

基于分析结果生成交易信号：

```python
def _generate_signal(self, timestamp, signal_type, price):
    return SignalEvent(
        symbol=self.symbol,
        timestamp=timestamp,
        signal_type=signal_type,  # "LONG" 或 "SHORT"
        strength=1.0,
        price=price
    )
```

## 3. 策略测试

### 3.1 单元测试

创建策略的单元测试：

```python
import unittest
from datetime import datetime

class TestTurnPointStrategy(unittest.TestCase):
    def setUp(self):
        self.strategy = TurnPointStrategy("TEST")
        
    def test_up_turn_point(self):
        # 创建向上转折点事件
        event = TurnPointEvent(
            timestamp=datetime.now(),
            direction="up",
            price=100.0
        )
        
        # 策略当前无持仓
        self.strategy.current_position = 0
        
        # 处理事件
        signals = self.strategy.process_event(event)
        
        # 验证生成买入信号
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0].signal_type, "LONG")
```

### 3.2 回测验证

使用回测系统验证策略：

```python
def test_strategy():
    # 创建配置
    config = {
        'symbol': '003021',
        'start_date': '20250101',
        'end_date': '20250630',
        'initial_cash': 100000
    }
    
    # 初始化系统
    trading_system = TradingSystem(config)
    
    # 设置策略
    trading_system.strategy = TurnPointStrategy(config['symbol'])
    
    # 运行回测
    trading_system.run_backtest()
    
    # 分析结果
    trading_system.show_results()

if __name__ == '__main__':
    test_strategy()
```

## 4. 策略优化

### 4.1 参数优化

示例参数优化过程：

```python
def optimize_parameters():
    best_performance = 0
    best_params = {}
    
    # 参数网格搜索
    for min_keep_days in [2, 3, 4, 5]:
        for min_extent in [0.03, 0.05, 0.07]:
            config = {
                'min_keep_days': min_keep_days,
                'min_extent_percent': min_extent * 100
            }
            
            # 运行回测
            performance = run_backtest(config)
            
            # 更新最优参数
            if performance > best_performance:
                best_performance = performance
                best_params = config
                
    return best_params
```

### 4.2 健壮性检验

检验策略在不同市场环境下的表现：

```python
def validate_robustness():
    # 牛市测试
    bull_market_performance = run_backtest(
        start_date='20240101',
        end_date='20240630'
    )
    
    # 熊市测试
    bear_market_performance = run_backtest(
        start_date='20230701',
        end_date='20231231'
    )
    
    # 震荡市测试
    sideways_market_performance = run_backtest(
        start_date='20250101',
        end_date='20250630'
    )
```

## 5. 注意事项

### 5.1 风险控制

- 设置止损止盈条件
- 控制持仓规模
- 分散投资标的
- 监控回撤风险

### 5.2 性能优化

- 使用向量化运算
- 缓存中间计算结果
- 优化事件处理逻辑
- 减少不必要的对象创建

### 5.3 实盘注意事项

- 考虑交易成本
- 处理部分成交情况
- 添加风控模块
- 实时监控持仓

## 6. 调试技巧

### 6.1 日志记录

```python
import logging

class MyStrategy(BaseStrategy):
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def process_event(self, event):
        self.logger.info(f"Processing event: {event}")
        # 策略逻辑
        self.logger.debug(f"Generated signals: {signals}")
```

### 6.2 数据可视化

```python
def visualize_signals(self, market_data, signals):
    import matplotlib.pyplot as plt
    
    plt.figure(figsize=(15, 8))
    plt.plot(market_data.index, market_data.close)
    
    # 标记买入点
    buy_points = signals[signals.signal_type == "LONG"]
    plt.scatter(buy_points.index, buy_points.price, 
                marker='^', color='g', label='Buy')
    
    # 标记卖出点
    sell_points = signals[signals.signal_type == "SHORT"]
    plt.scatter(sell_points.index, sell_points.price, 
                marker='v', color='r', label='Sell')
    
    plt.legend()
    plt.show()
```

## 结语

通过本示例，您可以了解如何在交易系统中开发和优化策略。记住，一个好的策略不仅要有清晰的交易逻辑，还要注重风险控制和实际可操作性。在实盘交易前，务必进行充分的回测和优化。
