from pyecharts import options as opts
from pyecharts.charts import Kline, Line
import numpy as np
from datetime import datetime, timedelta
import akshare as ak

class StockTrendCalculator:
    def __init__(self, window_size=60):
        """
        初始化股票趋势线计算器
        :param window_size: 时间窗口大小（默认60天）
        """
        self.window_size = window_size
        self.records = []  # 存储窗口内的记录 [{'date': date, 'high': high, 'low': low, 'day_index': index}, ...]
        self.day_counter = 0  # 天数计数器

    def add_record(self, timestamp, high, low):
        """
        添加新的股票记录，逐条处理
        :param timestamp: 时间戳（或日期对象）
        :param high: 当日最高价
        :param low: 当日最低价
        """
        # 添加新记录
        record = {
            'date': timestamp,
            'high': high,
            'low': low,
            'day_index': self.day_counter
        }
        self.records.append(record)
        self.day_counter += 1

        # 维护窗口大小，移除超出窗口的旧数据
        if len(self.records) > self.window_size:
            self.records.pop(0)

    def get_current_trend_lines(self):
        """
        获取当前的高点和低点趋势线参数
        :return: (high_line_params, low_line_params)
                 每个参数为 (slope, intercept, start_day, end_day)
        """
        if len(self.records) < 2:
            return None, None

        # 提取高点和低点数据
        high_points = [(r['day_index'], r['high']) for r in self.records]
        low_points = [(r['day_index'], r['low']) for r in self.records]

        # 计算趋势线
        high_line = self._fit_weighted_line(high_points)
        low_line = self._fit_weighted_line(low_points)

        return high_line, low_line

    def _fit_weighted_line(self, points):
        """
        使用加权最小二乘法拟合直线，给近期点更高权重
        :param points: [(day_index, price), ...] 点列表
        :return: (slope, intercept, start_day, end_day)
        """
        if len(points) < 2:
            return None

        n = len(points)
        # 提取x和y坐标
        x_values = [p[0] for p in points]
        y_values = [p[1] for p in points]

        # 计算权重：指数衰减，最新的点权重最高
        # 权重公式：w_i = exp(-(n-1-i) * decay_factor)
        decay_factor = 0.1  # 衰减因子，可调整
        weights = [np.exp(-(n-1-i) * decay_factor) for i in range(n)]

        # 加权最小二乘法计算
        sum_w = sum(weights)
        sum_wx = sum(w * x for w, x in zip(weights, x_values))
        sum_wy = sum(w * y for w, y in zip(weights, y_values))
        sum_wxx = sum(w * x * x for w, x in zip(weights, x_values))
        sum_wxy = sum(w * x * y for w, x, y in zip(weights, x_values, y_values))

        # 计算斜率和截距
        denominator = sum_w * sum_wxx - sum_wx * sum_wx
        if abs(denominator) < 1e-10:  # 避免除零
            # 如果分母接近0，返回水平线
            slope = 0
            intercept = sum_wy / sum_w
        else:
            slope = (sum_w * sum_wxy - sum_wx * sum_wy) / denominator
            intercept = (sum_wy * sum_wxx - sum_wx * sum_wxy) / denominator

        start_day = min(x_values)
        end_day = max(x_values)

        return slope, intercept, start_day, end_day

    def get_trend_line_values(self, line_params, day_indices):
        """
        根据趋势线参数计算指定天数的趋势线值
        :param line_params: (slope, intercept, start_day, end_day)
        :param day_indices: 要计算的天数索引列表
        :return: 趋势线值列表
        """
        if line_params is None:
            return [0] * len(day_indices)

        slope, intercept, _, _ = line_params  # 忽略start_day和end_day
        return [slope * day + intercept for day in day_indices]

    def get_current_trend_values(self):
        """
        获取当前趋势线的最新值（兼容旧接口）
        :return: (current_high_trend, current_low_trend)
        """
        if not self.records:
            return 0, 0

        high_line, low_line = self.get_current_trend_lines()
        current_day = self.records[-1]['day_index']

        if high_line is None or low_line is None:
            return 0, 0

        high_values = self.get_trend_line_values(high_line, [current_day])
        low_values = self.get_trend_line_values(low_line, [current_day])

        return high_values[0], low_values[0]



def visualize_stock_trends(data, window_size=60):
    """
    可视化股票数据和趋势线（逐条处理，实时更新趋势线）
    :param data: 股票数据列表，格式为 [{'date': '2023-01-01', 'open': 100, 'close': 102, 'low': 99, 'high': 105}, ...]
    :param window_size: 趋势线计算窗口大小
    """
    # 初始化趋势计算器
    calculator = StockTrendCalculator(window_size=window_size)

    # 准备数据容器
    dates = []           # 日期列表
    kline_data = []      # K线图数据: [[open, close, low, high], ...]
    high_trends = []     # 高点趋势值
    low_trends = []      # 低点趋势值
    day_indices = []     # 天数索引

    print(f"开始逐条处理 {len(data)} 条股票数据...")

    # 逐条处理每条数据（模拟实时数据流）
    for i, record in enumerate(data):
        # 添加记录到计算器（逐条处理）
        calculator.add_record(
            timestamp=record['date'],
            high=record['high'],
            low=record['low']
        )

        # 收集基础数据
        dates.append(record['date'])
        kline_data.append([record['open'], record['close'], record['low'], record['high']])
        day_indices.append(i)

        # 获取当前的趋势线参数
        high_line, low_line = calculator.get_current_trend_lines()

        # 计算当前趋势线值
        if high_line is not None and low_line is not None:
            high_trend_value = calculator.get_trend_line_values(high_line, [i])[0]
            low_trend_value = calculator.get_trend_line_values(low_line, [i])[0]
        else:
            high_trend_value = record['high']  # 数据不足时使用实际值
            low_trend_value = record['low']

        high_trends.append(high_trend_value)
        low_trends.append(low_trend_value)

        # 每处理100条数据打印一次进度
        if (i + 1) % 100 == 0:
            print(f"已处理 {i + 1} 条数据，当前趋势线值：高点={high_trend_value:.2f}, 低点={low_trend_value:.2f}")

    print(f"数据处理完成！共处理 {len(data)} 条记录")

    # 获取最终的趋势线参数用于显示
    final_high_line, final_low_line = calculator.get_current_trend_lines()
    if final_high_line:
        slope_h, intercept_h, _, _ = final_high_line
        print(f"最终高点趋势线：斜率={slope_h:.6f}, 截距={intercept_h:.2f}")
    if final_low_line:
        slope_l, intercept_l, _, _ = final_low_line
        print(f"最终低点趋势线：斜率={slope_l:.6f}, 截距={intercept_l:.2f}")
    
    # 创建K线图并叠加趋势线
    kline = (
        Kline()
        .add_xaxis(dates)
        .add_yaxis(
            series_name="K线",
            y_axis=kline_data,
            itemstyle_opts=opts.ItemStyleOpts(
                color="#ec0000",
                color0="#00da3c",
                border_color="#8A0000",
                border_color0="#008F28",
            ),
        )
        .set_global_opts(
            title_opts=opts.TitleOpts(title="股票趋势分析", subtitle="K线图+高低点趋势线"),
            xaxis_opts=opts.AxisOpts(
                type_="category",
                is_scale=True,
                boundary_gap=False,
                axisline_opts=opts.AxisLineOpts(is_on_zero=False),
                splitline_opts=opts.SplitLineOpts(is_show=False),
                split_number=20,
                min_="dataMin",
                max_="dataMax",
            ),
            yaxis_opts=opts.AxisOpts(
                is_scale=True,
                splitarea_opts=opts.SplitAreaOpts(
                    is_show=True, areastyle_opts=opts.AreaStyleOpts(opacity=1)
                ),
            ),
            tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="line"),
            datazoom_opts=[
                opts.DataZoomOpts(
                    is_show=False,
                    type_="inside",
                    range_start=0,
                    range_end=100,
                ),
                opts.DataZoomOpts(
                    is_show=True,
                    type_="slider",
                    pos_top="90%",
                    range_start=0,
                    range_end=100,
                ),
            ],
            legend_opts=opts.LegendOpts(
                is_show=True, pos_top="1%", pos_left="center"
            ),
        )
    )

    # 创建趋势线图并叠加到K线图上
    trend_line = (
        Line()
        .add_xaxis(dates)
        .add_yaxis(
            series_name="高点趋势线",
            y_axis=high_trends,
            is_smooth=False,  # 改为False，显示直线
            is_symbol_show=False,
            linestyle_opts=opts.LineStyleOpts(width=3, color="#FF4500", type_="solid"),
            label_opts=opts.LabelOpts(is_show=False),
        )
        .add_yaxis(
            series_name="低点趋势线",
            y_axis=low_trends,
            is_smooth=False,  # 改为False，显示直线
            is_symbol_show=False,
            linestyle_opts=opts.LineStyleOpts(width=3, color="#1E90FF", type_="solid"),
            label_opts=opts.LabelOpts(is_show=False),
        )
        .set_global_opts(
            xaxis_opts=opts.AxisOpts(
                type_="category",
                axislabel_opts=opts.LabelOpts(is_show=False),
            ),
            yaxis_opts=opts.AxisOpts(
                axislabel_opts=opts.LabelOpts(is_show=False),
                axisline_opts=opts.AxisLineOpts(is_show=False),
                axistick_opts=opts.AxisTickOpts(is_show=False),
                splitline_opts=opts.SplitLineOpts(is_show=False),
            ),
            legend_opts=opts.LegendOpts(is_show=False),
        )
    )

    # 将趋势线叠加到K线图上
    kline.overlap(trend_line)
    
    # 直接渲染K线图（已包含趋势线）
    kline.render("stock_trend_analysis.html")
    print("可视化图表已保存为 stock_trend_analysis.html")

# 生成示例数据
def get_data(stock_code: str, start_date: str, end_date: str):
    """生成示例股票数据"""
    df = ak.stock_zh_a_hist(
        symbol=stock_code, 
        period="daily", 
        start_date=start_date, 
        end_date=end_date, 
        # adjust="qfq"
    )
    data = []
    for _, row in df.iterrows():   
        data.append({
            'date': row['日期'],
            'open': row['开盘'],
            'close': row['收盘'],
            'high': row['最高'],
            'low': row['最低']
        })
        
    return data

if __name__ == "__main__":
    # 生成示例数据
    sample_data = get_data(stock_code='003021', start_date='20250101', end_date='20250630') 
    
    # 可视化股票趋势
    visualize_stock_trends(sample_data, window_size=20)