import numpy as np
import akshare as ak

class StockTrendCalculator:
    def __init__(self, window_size=60):
        """
        初始化股票趋势线计算器
        :param window_size: 时间窗口大小（默认60天）
        """
        self.window_size = window_size
        self.records = []  # 存储窗口内的记录 [{'date': date, 'high': high, 'low': low, 'day_index': index}, ...]
        self.day_counter = 0  # 天数计数器

    def add_record(self, timestamp, high, low):
        """
        添加新的股票记录，逐条处理
        :param timestamp: 时间戳（或日期对象）
        :param high: 当日最高价
        :param low: 当日最低价
        """
        # 添加新记录
        record = {
            'date': timestamp,
            'high': high,
            'low': low,
            'day_index': self.day_counter
        }
        self.records.append(record)
        self.day_counter += 1

        # 维护窗口大小，移除超出窗口的旧数据
        if len(self.records) > self.window_size:
            self.records.pop(0)

    def get_current_trend_lines(self):
        """
        获取当前的高点和低点趋势线参数
        :return: (high_line_params, low_line_params)
                 每个参数为 (slope, intercept, start_day, end_day)
        """
        if len(self.records) < 2:
            return None, None

        # 提取高点和低点数据
        high_points = [(r['day_index'], r['high']) for r in self.records]
        low_points = [(r['day_index'], r['low']) for r in self.records]

        # 计算趋势线
        high_line = self._fit_weighted_line(high_points)
        low_line = self._fit_weighted_line(low_points)

        return high_line, low_line

    def _fit_weighted_line(self, points):
        """
        使用加权最小二乘法拟合直线，给近期点更高权重
        :param points: [(day_index, price), ...] 点列表
        :return: (slope, intercept, start_day, end_day)
        """
        if len(points) < 2:
            return None

        n = len(points)
        # 提取x和y坐标
        x_values = [p[0] for p in points]
        y_values = [p[1] for p in points]

        # 计算权重：指数衰减，最新的点权重最高
        # 权重公式：w_i = exp(-(n-1-i) * decay_factor)
        decay_factor = 0.1  # 衰减因子，可调整
        weights = [np.exp(-(n-1-i) * decay_factor) for i in range(n)]

        # 加权最小二乘法计算
        sum_w = sum(weights)
        sum_wx = sum(w * x for w, x in zip(weights, x_values))
        sum_wy = sum(w * y for w, y in zip(weights, y_values))
        sum_wxx = sum(w * x * x for w, x in zip(weights, x_values))
        sum_wxy = sum(w * x * y for w, x, y in zip(weights, x_values, y_values))

        # 计算斜率和截距
        denominator = sum_w * sum_wxx - sum_wx * sum_wx
        if abs(denominator) < 1e-10:  # 避免除零
            # 如果分母接近0，返回水平线
            slope = 0
            intercept = sum_wy / sum_w
        else:
            slope = (sum_w * sum_wxy - sum_wx * sum_wy) / denominator
            intercept = (sum_wy * sum_wxx - sum_wx * sum_wxy) / denominator

        start_day = min(x_values)
        end_day = max(x_values)

        return slope, intercept, start_day, end_day

    def get_trend_line_values(self, line_params, day_indices):
        """
        根据趋势线参数计算指定天数的趋势线值
        :param line_params: (slope, intercept, start_day, end_day)
        :param day_indices: 要计算的天数索引列表
        :return: 趋势线值列表
        """
        if line_params is None:
            return [0] * len(day_indices)

        slope, intercept, _, _ = line_params  # 忽略start_day和end_day
        return [slope * day + intercept for day in day_indices]

    def get_current_trend_values(self):
        """
        获取当前趋势线的最新值（兼容旧接口）
        :return: (current_high_trend, current_low_trend)
        """
        if not self.records:
            return 0, 0

        high_line, low_line = self.get_current_trend_lines()
        current_day = self.records[-1]['day_index']

        if high_line is None or low_line is None:
            return 0, 0

        high_values = self.get_trend_line_values(high_line, [current_day])
        low_values = self.get_trend_line_values(low_line, [current_day])

        return high_values[0], low_values[0]



def create_interactive_chart(data, window_size=60):
    """
    创建交互式股票趋势分析页面
    :param data: 股票数据列表
    :param window_size: 趋势线计算窗口大小
    """
    # 将数据转换为JSON格式供前端使用
    import json

    # 准备数据
    stock_data = []
    for i, record in enumerate(data):
        stock_data.append({
            'date': str(record['date']),
            'open': float(record['open']),
            'close': float(record['close']),
            'high': float(record['high']),
            'low': float(record['low']),
            'index': i
        })

    # 创建HTML页面
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>交互式股票趋势分析</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .controls {{
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }}
        .btn {{
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }}
        .btn:hover {{
            background-color: #0056b3;
        }}
        .btn:disabled {{
            background-color: #6c757d;
            cursor: not-allowed;
        }}
        .info {{
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 3px;
            font-family: monospace;
        }}
        #chart {{
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }}
        .stats {{
            display: flex;
            justify-content: space-around;
            margin: 15px 0;
        }}
        .stat-item {{
            text-align: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }}
        .stat-value {{
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }}
        .stat-label {{
            font-size: 12px;
            color: #6c757d;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📈 交互式股票趋势分析</h1>

        <div class="controls">
            <button class="btn" onclick="addNextDay()" id="nextBtn">添加下一个交易日</button>
            <button class="btn" onclick="resetChart()">重置图表</button>
            <button class="btn" onclick="autoPlay()" id="autoBtn">自动播放</button>
            <button class="btn" onclick="stopAuto()" id="stopBtn" disabled>停止播放</button>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="currentDay">0</div>
                <div class="stat-label">当前交易日</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalDays">{len(stock_data)}</div>
                <div class="stat-label">总交易日</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="highSlope">--</div>
                <div class="stat-label">高点趋势斜率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="lowSlope">--</div>
                <div class="stat-label">低点趋势斜率</div>
            </div>
        </div>

        <div class="info" id="info">
            点击"添加下一个交易日"开始分析，或点击"自动播放"观看完整过程
        </div>

        <div id="chart"></div>
    </div>

    <script>
        // 趋势线计算器类（JavaScript版本）
        class StockTrendCalculator {{
            constructor(windowSize) {{
                this.windowSize = windowSize;
                this.records = [];
                this.dayCounter = 0;
            }}

            addRecord(timestamp, high, low) {{
                const record = {{
                    date: timestamp,
                    high: high,
                    low: low,
                    dayIndex: this.dayCounter
                }};
                this.records.push(record);
                this.dayCounter++;

                if (this.records.length > this.windowSize) {{
                    this.records.shift();
                }}
            }}

            getCurrentTrendLines() {{
                if (this.records.length < 2) {{
                    return [null, null];
                }}

                const highPoints = this.records.map(r => [r.dayIndex, r.high]);
                const lowPoints = this.records.map(r => [r.dayIndex, r.low]);

                const highLine = this.fitWeightedLine(highPoints);
                const lowLine = this.fitWeightedLine(lowPoints);

                return [highLine, lowLine];
            }}

            fitWeightedLine(points) {{
                if (points.length < 2) return null;

                const n = points.length;
                const xValues = points.map(p => p[0]);
                const yValues = points.map(p => p[1]);

                // 计算权重：指数衰减
                const decayFactor = 0.1;
                const weights = [];
                for (let i = 0; i < n; i++) {{
                    weights.push(Math.exp(-(n-1-i) * decayFactor));
                }}

                // 加权最小二乘法
                const sumW = weights.reduce((a, b) => a + b, 0);
                const sumWx = weights.reduce((sum, w, i) => sum + w * xValues[i], 0);
                const sumWy = weights.reduce((sum, w, i) => sum + w * yValues[i], 0);
                const sumWxx = weights.reduce((sum, w, i) => sum + w * xValues[i] * xValues[i], 0);
                const sumWxy = weights.reduce((sum, w, i) => sum + w * xValues[i] * yValues[i], 0);

                const denominator = sumW * sumWxx - sumWx * sumWx;
                let slope, intercept;

                if (Math.abs(denominator) < 1e-10) {{
                    slope = 0;
                    intercept = sumWy / sumW;
                }} else {{
                    slope = (sumW * sumWxy - sumWx * sumWy) / denominator;
                    intercept = (sumWy * sumWxx - sumWx * sumWxy) / denominator;
                }}

                return {{
                    slope: slope,
                    intercept: intercept,
                    startDay: Math.min(...xValues),
                    endDay: Math.max(...xValues)
                }};
            }}

            getTrendLineValues(lineParams, dayIndices) {{
                if (!lineParams) return dayIndices.map(() => 0);
                return dayIndices.map(day => lineParams.slope * day + lineParams.intercept);
            }}
        }}

        // 股票数据和初始化
        const stockData = {json.dumps(stock_data, indent=2)};
        const windowSize = {window_size};

        // 当前状态
        let currentIndex = 0;
        let calculator = new StockTrendCalculator(windowSize);
        let autoPlayInterval = null;

        // 图表实例
        let chart = echarts.init(document.getElementById('chart'));

        // 更新图表
        function updateChart() {{
            if (currentIndex === 0) {{
                chart.clear();
                return;
            }}

            // 获取当前显示的数据
            const currentData = stockData.slice(0, currentIndex);
            const dates = currentData.map(d => d.date);
            const klineData = currentData.map(d => [d.open, d.close, d.low, d.high]);

            // 计算趋势线
            const [highLine, lowLine] = calculator.getCurrentTrendLines();
            const dayIndices = currentData.map((_, i) => i);

            let highTrends = [];
            let lowTrends = [];
            let highSlope = 0, lowSlope = 0;

            if (highLine && lowLine) {{
                highTrends = calculator.getTrendLineValues(highLine, dayIndices);
                lowTrends = calculator.getTrendLineValues(lowLine, dayIndices);
                highSlope = highLine.slope;
                lowSlope = lowLine.slope;
            }}

            // 更新统计信息
            document.getElementById('currentDay').textContent = currentIndex;
            document.getElementById('highSlope').textContent = highSlope.toFixed(6);
            document.getElementById('lowSlope').textContent = lowSlope.toFixed(6);

            // 图表配置
            const option = {{
                title: {{
                    text: '交互式股票趋势分析',
                    left: 'center'
                }},
                tooltip: {{
                    trigger: 'axis',
                    axisPointer: {{
                        type: 'cross'
                    }}
                }},
                legend: {{
                    data: ['K线', '高点趋势线', '低点趋势线'],
                    top: 30
                }},
                grid: {{
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                }},
                xAxis: {{
                    type: 'category',
                    data: dates,
                    scale: true,
                    boundaryGap: false,
                    axisLine: {{ onZero: false }},
                    splitLine: {{ show: false }},
                    min: 'dataMin',
                    max: 'dataMax'
                }},
                yAxis: {{
                    scale: true,
                    splitArea: {{
                        show: true
                    }}
                }},
                dataZoom: [
                    {{
                        type: 'inside',
                        start: 0,
                        end: 100
                    }},
                    {{
                        show: true,
                        type: 'slider',
                        top: '90%',
                        start: 0,
                        end: 100
                    }}
                ],
                series: [
                    {{
                        name: 'K线',
                        type: 'candlestick',
                        data: klineData,
                        itemStyle: {{
                            color: '#ec0000',
                            color0: '#00da3c',
                            borderColor: '#8A0000',
                            borderColor0: '#008F28'
                        }}
                    }},
                    {{
                        name: '高点趋势线',
                        type: 'line',
                        data: highTrends,
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {{
                            color: '#FF0000',
                            width: 3
                        }}
                    }},
                    {{
                        name: '低点趋势线',
                        type: 'line',
                        data: lowTrends,
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {{
                            color: '#00AA00',
                            width: 3
                        }}
                    }}
                ]
            }};

            chart.setOption(option);
        }}

        // 添加下一个交易日
        function addNextDay() {{
            if (currentIndex >= stockData.length) {{
                document.getElementById('info').textContent = '所有数据已添加完毕！';
                document.getElementById('nextBtn').disabled = true;
                return;
            }}

            const record = stockData[currentIndex];
            calculator.addRecord(record.date, record.high, record.low);
            currentIndex++;

            updateChart();

            document.getElementById('info').textContent =
                `已添加第 ${{currentIndex}} 个交易日: ${{record.date}}, 高: ${{record.high}}, 低: ${{record.low}}`;

            if (currentIndex >= stockData.length) {{
                document.getElementById('nextBtn').disabled = true;
                document.getElementById('info').textContent += ' - 所有数据已添加完毕！';
            }}
        }}

        // 重置图表
        function resetChart() {{
            currentIndex = 0;
            calculator = new StockTrendCalculator(windowSize);
            chart.clear();
            document.getElementById('nextBtn').disabled = false;
            document.getElementById('currentDay').textContent = '0';
            document.getElementById('highSlope').textContent = '--';
            document.getElementById('lowSlope').textContent = '--';
            document.getElementById('info').textContent = '图表已重置，点击"添加下一个交易日"开始分析';
            stopAuto();
        }}

        // 自动播放
        function autoPlay() {{
            if (autoPlayInterval) return;

            document.getElementById('autoBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;

            autoPlayInterval = setInterval(() => {{
                if (currentIndex >= stockData.length) {{
                    stopAuto();
                    return;
                }}
                addNextDay();
            }}, 500); // 每500ms添加一个数据点
        }}

        // 停止自动播放
        function stopAuto() {{
            if (autoPlayInterval) {{
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
            }}
            document.getElementById('autoBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
        }}

        // 初始化图表
        updateChart();
    </script>
</body>
</html>
    """

    # 保存HTML文件
    with open("interactive_stock_analysis.html", "w", encoding="utf-8") as f:
        f.write(html_content)

    print("交互式股票分析页面已保存为 interactive_stock_analysis.html")

def visualize_stock_trends(data, window_size=60):
    """
    创建交互式可视化（替代原来的静态图表）
    """
    create_interactive_chart(data, window_size)

# 生成示例数据
def get_data(stock_code: str, start_date: str, end_date: str):
    """生成示例股票数据"""
    df = ak.stock_zh_a_hist(
        symbol=stock_code, 
        period="daily", 
        start_date=start_date, 
        end_date=end_date, 
        # adjust="qfq"
    )
    data = []
    for _, row in df.iterrows():   
        data.append({
            'date': row['日期'],
            'open': row['开盘'],
            'close': row['收盘'],
            'high': row['最高'],
            'low': row['最低']
        })
        
    return data

if __name__ == "__main__":
    # 生成示例数据
    sample_data = get_data(stock_code='003021', start_date='20250101', end_date='20250630') 
    
    # 可视化股票趋势
    visualize_stock_trends(sample_data, window_size=20)