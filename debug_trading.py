from pyecharts import options as opts
from pyecharts.charts import Kline, Line, Grid
import numpy as np
from datetime import datetime, timedelta
import akshare as ak

class StockTrendCalculator:
    def __init__(self, window_size=60):
        """
        初始化股票趋势线计算器
        :param window_size: 时间窗口大小（默认60天）
        """
        self.window_size = window_size
        self.high_prices = []  # 存储窗口内的高点价格
        self.low_prices = []   # 存储窗口内的低点价格
        self.timestamps = []   # 存储时间戳（或日期索引）

    def add_record(self, timestamp, high, low):
        """
        添加新的股票记录
        :param timestamp: 时间戳（或日期对象）
        :param high: 当日最高价
        :param low: 当日最低价
        """
        # 添加新数据
        self.timestamps.append(timestamp)
        self.high_prices.append(high)
        self.low_prices.append(low)
        
        # 维护窗口大小
        if len(self.timestamps) > self.window_size:
            self.timestamps.pop(0)
            self.high_prices.pop(0)
            self.low_prices.pop(0)

    def calculate_trend_lines(self):
        """
        计算高点和低点的趋势线
        :return: (high_slope, high_intercept), (low_slope, low_intercept)
                 分别表示高点和低点趋势线的斜率和截距
        """
        n = len(self.timestamps)
        if n < 2:
            # 数据不足时返回水平线
            return (0, sum(self.high_prices)/max(1, n)), (0, sum(self.low_prices)/max(1, n))
        
        # 计算时间索引（0 = 最早，n-1 = 最新）
        time_indices = list(range(n))
        
        # 计算高点和低点的加权线性回归
        high_slope, high_intercept = self._weighted_linear_regression(
            time_indices, self.high_prices, n
        )
        low_slope, low_intercept = self._weighted_linear_regression(
            time_indices, self.low_prices, n
        )
        
        return (high_slope, high_intercept), (low_slope, low_intercept)

    def _weighted_linear_regression(self, x, y, n):
        """
        加权线性回归（最小二乘法）
        :param x: 时间索引列表
        :param y: 价格列表
        :param n: 数据点数量
        :return: (slope, intercept)
        """
        # 权重计算：指数衰减（近期数据权重更高）
        weights = [pow(0.95, n-1-i) for i in range(n)]
        
        # 计算加权平均值
        sum_w = sum(weights)
        wmean_x = sum(w * xi for w, xi in zip(weights, x)) / sum_w
        wmean_y = sum(w * yi for w, yi in zip(weights, y)) / sum_w
        
        # 计算协方差和方差
        cov_xy = sum(w * (xi - wmean_x) * (yi - wmean_y) for w, xi, yi in zip(weights, x, y))
        var_x = sum(w * pow(xi - wmean_x, 2) for w, xi in zip(weights, x))
        
        # 计算斜率和截距
        slope = cov_xy / var_x if var_x != 0 else 0
        intercept = wmean_y - slope * wmean_x
        
        return slope, intercept

    def get_current_trend_values(self):
        """
        获取当前趋势线的最新值
        :return: (current_high_trend, current_low_trend)
        """
        if not self.timestamps:
            return 0, 0
        
        n = len(self.timestamps)
        (high_slope, high_intercept), (low_slope, low_intercept) = self.calculate_trend_lines()
        
        # 最新点的时间索引 = n-1
        current_high = high_slope * (n-1) + high_intercept
        current_low = low_slope * (n-1) + low_intercept
        
        return current_high, current_low



def visualize_stock_trends(data, window_size=60):
    """
    可视化股票数据和趋势线
    :param data: 股票数据列表，格式为 [{'date': '2023-01-01', 'open': 100, 'close': 102, 'low': 99, 'high': 105}, ...]
    :param window_size: 趋势线计算窗口大小
    """
    # 初始化趋势计算器
    calculator = StockTrendCalculator(window_size=window_size)
    
    # 准备数据容器
    dates = []           # 日期列表
    kline_data = []      # K线图数据: [[open, close, low, high], ...]
    high_trends = []     # 高点趋势值
    low_trends = []      # 低点趋势值
    actual_highs = []    # 实际高点
    actual_lows = []     # 实际低点
    
    # 处理每条数据
    for record in data:
        # 添加记录到计算器
        calculator.add_record(
            timestamp=record['date'],
            high=record['high'],
            low=record['low']
        )
        
        # 获取当前趋势值
        high_trend, low_trend = calculator.get_current_trend_values()
        
        # 收集数据
        dates.append(record['date'])
        kline_data.append([record['open'], record['close'], record['low'], record['high']])
        high_trends.append(high_trend)
        low_trends.append(low_trend)
        actual_highs.append(record['high'])
        actual_lows.append(record['low'])
    
    # 创建K线图
    kline = (
        Kline()
        .add_xaxis(dates)
        .add_yaxis(
            series_name="K线",
            y_axis=kline_data,
            itemstyle_opts=opts.ItemStyleOpts(
                color="#ec0000",
                color0="#00da3c",
                border_color="#8A0000",
                border_color0="#008F28",
            ),
        )
        .set_global_opts(
            title_opts=opts.TitleOpts(title="股票趋势分析", subtitle="K线图+高低点趋势线"),
            xaxis_opts=opts.AxisOpts(
                type_="category",
                is_scale=True,
                boundary_gap=False,
                axisline_opts=opts.AxisLineOpts(is_on_zero=False),
                splitline_opts=opts.SplitLineOpts(is_show=False),
                split_number=20,
                min_="dataMin",
                max_="dataMax",
            ),
            yaxis_opts=opts.AxisOpts(
                is_scale=True,
                splitarea_opts=opts.SplitAreaOpts(
                    is_show=True, areastyle_opts=opts.AreaStyleOpts(opacity=1)
                ),
            ),
            tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="line"),
            datazoom_opts=[
                opts.DataZoomOpts(
                    is_show=False,
                    type_="inside",
                    xaxis_index=[0, 1],
                    range_start=0,
                    range_end=100,
                ),
                opts.DataZoomOpts(
                    is_show=True,
                    xaxis_index=[0, 1],
                    type_="slider",
                    pos_top="85%",
                    range_start=0,
                    range_end=100,
                ),
            ],
            legend_opts=opts.LegendOpts(
                is_show=True, pos_top="1%", pos_left="center"
            ),
        )
    )
    
    
    # 创建趋势线图
    trend_line = (
        Line()
        .add_xaxis(dates)
        .add_yaxis(
            series_name="高点趋势线",
            y_axis=high_trends,
            is_smooth=True,
            is_symbol_show=False,
            linestyle_opts=opts.LineStyleOpts(width=3, color="#FF4500"),
            label_opts=opts.LabelOpts(is_show=False),
        )
        .add_yaxis(
            series_name="低点趋势线",
            y_axis=low_trends,
            is_smooth=True,
            is_symbol_show=False,
            linestyle_opts=opts.LineStyleOpts(width=3, color="#1E90FF"),
            label_opts=opts.LabelOpts(is_show=False),
        )
        .add_yaxis(
            series_name="实际高点",
            y_axis=actual_highs,
            is_symbol_show=True,
            symbol="circle",
            symbol_size=6,
            label_opts=opts.LabelOpts(is_show=False),
            linestyle_opts=opts.LineStyleOpts(width=0),
            itemstyle_opts=opts.ItemStyleOpts(color="#FF4500"),
        )
        .add_yaxis(
            series_name="实际低点",
            y_axis=actual_lows,
            is_symbol_show=True,
            symbol="circle",
            symbol_size=6,
            label_opts=opts.LabelOpts(is_show=False),
            linestyle_opts=opts.LineStyleOpts(width=0),
            itemstyle_opts=opts.ItemStyleOpts(color="#1E90FF"),
        )
        .set_global_opts(
            xaxis_opts=opts.AxisOpts(
                type_="category",
                grid_index=1,
                axislabel_opts=opts.LabelOpts(is_show=False),
            ),
            yaxis_opts=opts.AxisOpts(
                grid_index=1,
                split_number=3,
                axisline_opts=opts.AxisLineOpts(is_on_zero=False),
                axistick_opts=opts.AxisTickOpts(is_show=False),
                splitline_opts=opts.SplitLineOpts(is_show=False),
            ),
            legend_opts=opts.LegendOpts(is_show=False),
        )
    )
    
    # 使用Grid组合图表
    grid_chart = Grid(init_opts=opts.InitOpts(width="1800px", height="900px"))
    
    # K线图占据主要区域
    grid_chart.add(
        kline,
        grid_opts=opts.GridOpts(
            pos_left="5%", pos_right="5%", pos_top="10%", height="60%"
        ),
    )
    
    # 趋势线图放在下方
    grid_chart.add(
        trend_line,
        grid_opts=opts.GridOpts(
            pos_left="5%", pos_right="5%", pos_top="75%", height="15%"
        ),
    )
    
    # 渲染图表到HTML文件
    grid_chart.render("stock_trend_analysis.html")
    print("可视化图表已保存为 stock_trend_analysis.html")

# 生成示例数据
def get_data(stock_code: str, start_date: str, end_date: str):
    """生成示例股票数据"""
    df = ak.stock_zh_a_hist(
        symbol=stock_code, 
        period="daily", 
        start_date=start_date, 
        end_date=end_date, 
        # adjust="qfq"
    )
    data = []
    for _, row in df.iterrows():   
        data.append({
            'date': row['日期'],
            'open': row['开盘'],
            'close': row['收盘'],
            'high': row['最高'],
            'low': row['最低']
        })
        
    return data

if __name__ == "__main__":
    # 生成示例数据
    sample_data = get_data(stock_code='003021', start_date='20250101', end_date='20250630') 
    
    # 可视化股票趋势
    visualize_stock_trends(sample_data, window_size=60)