#!/usr/bin/env python3
"""
调试策略 - 找出为什么没有交易

分析策略条件和信号生成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
from typing import Dict, Any

from trading_system.core import EventEngine, MarketEvent, SignalEvent, OrderEvent, FillEvent
from trading_system.data import HistoricalDataProvider
from trading_system.analysis import IndicatorCalculator
from trading_system.strategies import SimpleTrendStrategy
from trading_system.execution import BacktestExecutionHandler
from trading_system.portfolio import PortfolioManager


def main():
    """主函数"""
    print("=" * 100)
    print("调试策略 - 找出为什么没有交易")
    print("=" * 100)
    
    try:
        # 配置
        config = {
            'symbol': '003021',
            'start_date': '20240101',  # 缩短时间范围
            'end_date': '20250630',
            'initial_cash': 100000,
            'slippage_pct': 0.001,
        }
        
        print(f"交易标的: {config['symbol']}")
        print(f"回测期间: {config['start_date']} - {config['end_date']}")
        print("-" * 100)
        
        # 设置日志
        logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
        logger = logging.getLogger(__name__)
        
        # 创建组件
        data_provider = HistoricalDataProvider(
            symbol=config['symbol'],
            start_date=config['start_date'],
            end_date=config['end_date']
        )
        indicator_calculator = IndicatorCalculator()
        
        # 分析数据
        print("分析市场数据和技术指标...")
        
        signal_conditions = []
        event_count = 0
        
        for market_event in data_provider.get_data_stream():
            event_count += 1
            
            # 计算技术指标
            indicators = indicator_calculator.calculate_all(market_event)
            market_event.indicators.update(indicators)
            
            current_price = market_event.close
            ma20 = indicators.get('MA20')
            rsi = indicators.get('RSI')
            
            if ma20 is not None and rsi is not None:
                # 检查买入条件
                buy_condition = current_price > ma20 and rsi < 70
                # 检查卖出条件  
                sell_condition = current_price < ma20 and rsi > 30
                
                condition_info = {
                    'date': market_event.timestamp.strftime('%Y-%m-%d'),
                    'price': current_price,
                    'ma20': ma20,
                    'rsi': rsi,
                    'price_vs_ma20': current_price / ma20 if ma20 > 0 else 0,
                    'buy_condition': buy_condition,
                    'sell_condition': sell_condition
                }
                signal_conditions.append(condition_info)
                
                # 打印前10个和后10个条件
                if event_count <= 10 or event_count > len(list(data_provider.get_data_stream())) - 10:
                    print(f"{condition_info['date']}: Price={current_price:.2f}, MA20={ma20:.2f}, RSI={rsi:.1f}, "
                          f"Buy={buy_condition}, Sell={sell_condition}")
        
        print(f"\n分析完成，共处理 {len(signal_conditions)} 条记录")
        
        # 统计条件满足情况
        buy_signals = [c for c in signal_conditions if c['buy_condition']]
        sell_signals = [c for c in signal_conditions if c['sell_condition']]
        
        print(f"\n📊 信号统计:")
        print(f"  满足买入条件的天数: {len(buy_signals)}")
        print(f"  满足卖出条件的天数: {len(sell_signals)}")
        
        if buy_signals:
            print(f"\n🟢 买入信号示例 (前5个):")
            for i, signal in enumerate(buy_signals[:5], 1):
                print(f"  {i}. {signal['date']}: Price={signal['price']:.2f} > MA20={signal['ma20']:.2f}, RSI={signal['rsi']:.1f} < 70")
        
        if sell_signals:
            print(f"\n🔴 卖出信号示例 (前5个):")
            for i, signal in enumerate(sell_signals[:5], 1):
                print(f"  {i}. {signal['date']}: Price={signal['price']:.2f} < MA20={signal['ma20']:.2f}, RSI={signal['rsi']:.1f} > 30")
        
        # 分析价格与MA20的关系
        price_above_ma20 = [c for c in signal_conditions if c['price_vs_ma20'] > 1.0]
        price_below_ma20 = [c for c in signal_conditions if c['price_vs_ma20'] < 1.0]
        
        print(f"\n📈 价格与MA20关系:")
        print(f"  价格高于MA20的天数: {len(price_above_ma20)} ({len(price_above_ma20)/len(signal_conditions)*100:.1f}%)")
        print(f"  价格低于MA20的天数: {len(price_below_ma20)} ({len(price_below_ma20)/len(signal_conditions)*100:.1f}%)")
        
        # 分析RSI分布
        rsi_values = [c['rsi'] for c in signal_conditions if c['rsi'] is not None]
        if rsi_values:
            avg_rsi = sum(rsi_values) / len(rsi_values)
            min_rsi = min(rsi_values)
            max_rsi = max(rsi_values)
            
            rsi_low = [r for r in rsi_values if r < 30]
            rsi_high = [r for r in rsi_values if r > 70]
            rsi_mid = [r for r in rsi_values if 30 <= r <= 70]
            
            print(f"\n📊 RSI分布:")
            print(f"  平均RSI: {avg_rsi:.1f}")
            print(f"  RSI范围: {min_rsi:.1f} - {max_rsi:.1f}")
            print(f"  RSI < 30 (超卖): {len(rsi_low)} 天 ({len(rsi_low)/len(rsi_values)*100:.1f}%)")
            print(f"  RSI > 70 (超买): {len(rsi_high)} 天 ({len(rsi_high)/len(rsi_values)*100:.1f}%)")
            print(f"  30 <= RSI <= 70: {len(rsi_mid)} 天 ({len(rsi_mid)/len(rsi_values)*100:.1f}%)")
        
        # 建议
        print(f"\n💡 策略建议:")
        if len(buy_signals) == 0:
            print("  ❌ 没有满足买入条件的情况，建议放宽买入条件")
            print("     - 降低RSI阈值 (从70降到80)")
            print("     - 或者改用价格突破MA5而不是MA20")
        
        if len(sell_signals) == 0:
            print("  ❌ 没有满足卖出条件的情况，建议放宽卖出条件")
            print("     - 提高RSI阈值 (从30提高到20)")
            print("     - 或者改用价格跌破MA5而不是MA20")
        
        print("=" * 100)
        
    except Exception as e:
        print(f"\n❌ 系统运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
