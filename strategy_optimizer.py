#!/usr/bin/env python3
"""
策略参数优化器

通过网格搜索和遗传算法优化策略参数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Tuple
import itertools
from concurrent.futures import ProcessPoolExecutor
import logging

from trading_system.strategies.enhanced_turn_point_strategy import EnhancedTurnPointStrategy
from strategy_backtest_framework import StrategyBacktester

class StrategyOptimizer:
    """策略参数优化器"""
    
    def __init__(self, data: pd.DataFrame, initial_capital: float = 100000):
        """
        初始化优化器
        
        Args:
            data: 历史数据
            initial_capital: 初始资金
        """
        self.data = data
        self.initial_capital = initial_capital
        self.logger = logging.getLogger(__name__)
    
    def grid_search(self, param_grid: Dict[str, List]) -> List[Dict[str, Any]]:
        """
        网格搜索优化
        
        Args:
            param_grid: 参数网格
            
        Returns:
            List[Dict[str, Any]]: 优化结果列表
        """
        print("开始网格搜索优化...")
        
        # 生成所有参数组合
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        param_combinations = list(itertools.product(*param_values))
        
        print(f"总共需要测试 {len(param_combinations)} 个参数组合")
        
        results = []
        
        for i, param_combo in enumerate(param_combinations):
            if i % 10 == 0:
                print(f"进度: {i}/{len(param_combinations)} ({i/len(param_combinations)*100:.1f}%)")
            
            # 创建参数字典
            params = dict(zip(param_names, param_combo))
            
            # 运行回测
            result = self._test_parameters(params)
            if result:
                results.append(result)
        
        # 按夏普比率排序
        results.sort(key=lambda x: x['sharpe_ratio'], reverse=True)
        
        print(f"网格搜索完成，找到 {len(results)} 个有效结果")
        return results
    
    def _test_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        测试单组参数
        
        Args:
            params: 参数字典
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            # 创建策略
            strategy = EnhancedTurnPointStrategy("TEST")
            
            # 设置参数
            for key, value in params.items():
                strategy.set_parameter(key, value)
            
            # 运行回测
            backtester = StrategyBacktester(self.initial_capital)
            results = backtester.run_backtest(strategy, self.data)
            
            # 添加参数信息
            results['parameters'] = params.copy()
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error testing parameters {params}: {e}")
            return None
    
    def optimize_key_parameters(self) -> Dict[str, Any]:
        """优化关键参数"""
        
        # 定义参数搜索空间
        param_grid = {
            'min_volume_confirm_strength': [0.4, 0.5, 0.6, 0.7, 0.8],
            'min_breakout_strength': [0.6, 0.7, 0.8, 0.9],
            'stop_loss_percent': [0.04, 0.06, 0.08, 0.10],
            'take_profit_percent': [0.08, 0.10, 0.12, 0.15],
            'max_position_hold_days': [8, 10, 12, 15]
        }
        
        # 运行网格搜索
        results = self.grid_search(param_grid)
        
        if results:
            best_result = results[0]
            print(f"\n=== 最优参数组合 ===")
            print(f"参数: {best_result['parameters']}")
            print(f"总收益率: {best_result['total_return']:.2%}")
            print(f"夏普比率: {best_result['sharpe_ratio']:.2f}")
            print(f"最大回撤: {best_result['max_drawdown']:.2%}")
            print(f"胜率: {best_result['win_rate']:.2%}")
            
            return best_result
        else:
            print("未找到有效的参数组合")
            return None

def generate_optimized_test_data(days: int = 120) -> pd.DataFrame:
    """生成用于优化的测试数据"""
    np.random.seed(456)
    
    base_price = 100.0
    prices = [base_price]
    volumes = []
    
    for i in range(days):
        # 创建更明显的趋势和转折点
        cycle = i // 20  # 每20天一个周期
        phase = i % 20
        
        if phase < 8:
            # 强上升趋势
            trend = 0.02 + 0.01 * np.sin(i * 0.2)
        elif phase < 12:
            # 转折整理
            trend = 0.005 * np.sin(i * 0.8)
        elif phase < 18:
            # 下降趋势
            trend = -0.018 + 0.008 * np.sin(i * 0.3)
        else:
            # 底部整理
            trend = 0.003 * np.sin(i * 1.2)
        
        # 添加周期性波动
        trend += 0.005 * np.sin(i * 0.05)
        
        noise = np.random.normal(0, 0.01)
        change = trend + noise
        
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
        
        # 成交量与趋势强度相关
        price_change = abs(change)
        base_volume = 2000000
        volume_multiplier = 1 + price_change * 30
        
        # 在转折点大幅放量
        if phase in [7, 8, 9, 11, 12, 13, 17, 18, 19]:
            volume_multiplier *= 5
        
        volume = base_volume * volume_multiplier * (1 + np.random.normal(0, 0.15))
        volumes.append(max(volume, base_volume * 0.4))
    
    dates = [datetime.now() - timedelta(days=days-i) for i in range(days+1)]
    
    df = pd.DataFrame({
        'date': dates,
        'close': prices
    })
    
    df['open'] = df['close'].shift(1).fillna(df['close'])
    df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.004, len(df)))
    df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.004, len(df)))
    df['volume'] = [2000000] + volumes
    
    return df.iloc[1:]

def test_optimized_strategy():
    """测试优化后的策略"""
    print("=== 策略参数优化测试 ===")
    
    # 生成优化用数据
    data = generate_optimized_test_data(100)
    print(f"生成了 {len(data)} 天的优化测试数据")
    
    # 创建优化器
    optimizer = StrategyOptimizer(data)
    
    # 运行优化
    best_result = optimizer.optimize_key_parameters()
    
    if best_result:
        print(f"\n=== 优化后策略回测 ===")
        
        # 使用最优参数创建策略
        optimized_strategy = EnhancedTurnPointStrategy("TEST")
        for key, value in best_result['parameters'].items():
            optimized_strategy.set_parameter(key, value)
        
        # 在新数据上测试
        test_data = generate_optimized_test_data(80)  # 不同的随机种子
        backtester = StrategyBacktester(100000)
        test_results = backtester.run_backtest(optimized_strategy, test_data)
        
        print(f"测试数据表现:")
        print(f"  总收益率: {test_results['total_return']:.2%}")
        print(f"  夏普比率: {test_results['sharpe_ratio']:.2f}")
        print(f"  最大回撤: {test_results['max_drawdown']:.2%}")
        print(f"  胜率: {test_results['win_rate']:.2%}")
        print(f"  交易次数: {test_results['total_trades']}")
        
        return best_result, test_results
    
    return None, None

def compare_strategies():
    """比较默认策略和优化策略"""
    print("\n=== 策略对比测试 ===")
    
    # 生成测试数据
    test_data = generate_optimized_test_data(100)
    
    # 测试默认策略
    default_strategy = EnhancedTurnPointStrategy("TEST")
    backtester1 = StrategyBacktester(100000)
    default_results = backtester1.run_backtest(default_strategy, test_data)
    
    # 测试优化策略
    optimized_strategy = EnhancedTurnPointStrategy("TEST")
    optimized_strategy.set_parameter('min_volume_confirm_strength', 0.5)
    optimized_strategy.set_parameter('min_breakout_strength', 0.7)
    optimized_strategy.set_parameter('stop_loss_percent', 0.06)
    optimized_strategy.set_parameter('take_profit_percent', 0.12)
    optimized_strategy.set_parameter('max_position_hold_days', 10)
    
    backtester2 = StrategyBacktester(100000)
    optimized_results = backtester2.run_backtest(optimized_strategy, test_data)
    
    # 对比结果
    print(f"\n策略对比结果:")
    print(f"{'指标':<20} {'默认策略':<15} {'优化策略':<15} {'改进':<10}")
    print("-" * 65)
    
    metrics = [
        ('总收益率', 'total_return', '{:.2%}'),
        ('夏普比率', 'sharpe_ratio', '{:.2f}'),
        ('最大回撤', 'max_drawdown', '{:.2%}'),
        ('胜率', 'win_rate', '{:.2%}'),
        ('交易次数', 'total_trades', '{:.0f}')
    ]
    
    for name, key, fmt in metrics:
        default_val = default_results[key]
        optimized_val = optimized_results[key]
        
        if key in ['total_return', 'sharpe_ratio', 'win_rate']:
            improvement = optimized_val - default_val
            improvement_str = f"+{improvement:.2%}" if improvement > 0 else f"{improvement:.2%}"
        elif key == 'max_drawdown':
            improvement = default_val - optimized_val  # 回撤越小越好
            improvement_str = f"+{improvement:.2%}" if improvement > 0 else f"{improvement:.2%}"
        else:
            improvement = optimized_val - default_val
            improvement_str = f"+{improvement:.0f}" if improvement > 0 else f"{improvement:.0f}"
        
        print(f"{name:<20} {fmt.format(default_val):<15} {fmt.format(optimized_val):<15} {improvement_str:<10}")
    
    return default_results, optimized_results

if __name__ == "__main__":
    # 运行优化测试
    best_result, test_results = test_optimized_strategy()
    
    # 运行对比测试
    default_results, optimized_results = compare_strategies()
    
    print("\n优化测试完成！")
