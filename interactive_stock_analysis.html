
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>交互式股票趋势分析</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .info {
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 3px;
            font-family: monospace;
        }
        #chart {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 15px 0;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📈 交互式股票趋势分析</h1>

        <div class="controls">
            <button class="btn" onclick="addNextDay()" id="nextBtn">添加下一个交易日</button>
            <button class="btn" onclick="resetChart()">重置图表</button>
            <button class="btn" onclick="autoPlay()" id="autoBtn">自动播放</button>
            <button class="btn" onclick="stopAuto()" id="stopBtn" disabled>停止播放</button>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="currentDay">0</div>
                <div class="stat-label">当前交易日</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalDays">113</div>
                <div class="stat-label">总交易日</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="highSlope">--</div>
                <div class="stat-label">高点趋势斜率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="lowSlope">--</div>
                <div class="stat-label">低点趋势斜率</div>
            </div>
        </div>

        <div class="info" id="info">
            点击"添加下一个交易日"开始分析，或点击"自动播放"观看完整过程
        </div>

        <div id="chart"></div>
    </div>

    <script>
        // 趋势线计算器类（JavaScript版本）
        class StockTrendCalculator {
            constructor(windowSize) {
                this.windowSize = windowSize;
                this.records = [];
                this.dayCounter = 0;
            }

            addRecord(timestamp, high, low) {
                const record = {
                    date: timestamp,
                    high: high,
                    low: low,
                    dayIndex: this.dayCounter
                };
                this.records.push(record);
                this.dayCounter++;

                if (this.records.length > this.windowSize) {
                    this.records.shift();
                }
            }

            getCurrentTrendLines() {
                if (this.records.length < 2) {
                    return [null, null];
                }

                const highPoints = this.records.map(r => [r.dayIndex, r.high]);
                const lowPoints = this.records.map(r => [r.dayIndex, r.low]);

                const highLine = this.fitWeightedLine(highPoints);
                const lowLine = this.fitWeightedLine(lowPoints);

                return [highLine, lowLine];
            }

            fitWeightedLine(points) {
                if (points.length < 2) return null;

                const n = points.length;
                const xValues = points.map(p => p[0]);
                const yValues = points.map(p => p[1]);

                // 计算权重：指数衰减
                const decayFactor = 0.1;
                const weights = [];
                for (let i = 0; i < n; i++) {
                    weights.push(Math.exp(-(n-1-i) * decayFactor));
                }

                // 加权最小二乘法
                const sumW = weights.reduce((a, b) => a + b, 0);
                const sumWx = weights.reduce((sum, w, i) => sum + w * xValues[i], 0);
                const sumWy = weights.reduce((sum, w, i) => sum + w * yValues[i], 0);
                const sumWxx = weights.reduce((sum, w, i) => sum + w * xValues[i] * xValues[i], 0);
                const sumWxy = weights.reduce((sum, w, i) => sum + w * xValues[i] * yValues[i], 0);

                const denominator = sumW * sumWxx - sumWx * sumWx;
                let slope, intercept;

                if (Math.abs(denominator) < 1e-10) {
                    slope = 0;
                    intercept = sumWy / sumW;
                } else {
                    slope = (sumW * sumWxy - sumWx * sumWy) / denominator;
                    intercept = (sumWy * sumWxx - sumWx * sumWxy) / denominator;
                }

                return {
                    slope: slope,
                    intercept: intercept,
                    startDay: Math.min(...xValues),
                    endDay: Math.max(...xValues)
                };
            }

            getTrendLineValues(lineParams, dayIndices) {
                if (!lineParams) return dayIndices.map(() => 0);
                return dayIndices.map(day => lineParams.slope * day + lineParams.intercept);
            }
        }

        // 股票数据和初始化
        const stockData = [
  {
    "date": "2025-01-02",
    "open": 73.89,
    "close": 74.98,
    "high": 76.97,
    "low": 71.96,
    "index": 0
  },
  {
    "date": "2025-01-03",
    "open": 75.0,
    "close": 70.05,
    "high": 75.06,
    "low": 69.8,
    "index": 1
  },
  {
    "date": "2025-01-06",
    "open": 69.15,
    "close": 68.58,
    "high": 70.19,
    "low": 67.7,
    "index": 2
  },
  {
    "date": "2025-01-07",
    "open": 68.79,
    "close": 71.2,
    "high": 71.2,
    "low": 68.5,
    "index": 3
  },
  {
    "date": "2025-01-08",
    "open": 71.04,
    "close": 78.32,
    "high": 78.32,
    "low": 70.18,
    "index": 4
  },
  {
    "date": "2025-01-09",
    "open": 78.43,
    "close": 86.15,
    "high": 86.15,
    "low": 77.92,
    "index": 5
  },
  {
    "date": "2025-01-10",
    "open": 87.5,
    "close": 92.0,
    "high": 94.77,
    "low": 86.67,
    "index": 6
  },
  {
    "date": "2025-01-13",
    "open": 90.5,
    "close": 85.99,
    "high": 92.0,
    "low": 83.2,
    "index": 7
  },
  {
    "date": "2025-01-14",
    "open": 84.56,
    "close": 93.53,
    "high": 93.53,
    "low": 84.1,
    "index": 8
  },
  {
    "date": "2025-01-15",
    "open": 89.99,
    "close": 97.92,
    "high": 99.47,
    "low": 89.15,
    "index": 9
  },
  {
    "date": "2025-01-16",
    "open": 97.0,
    "close": 96.11,
    "high": 102.3,
    "low": 94.3,
    "index": 10
  },
  {
    "date": "2025-01-17",
    "open": 95.15,
    "close": 94.5,
    "high": 96.41,
    "low": 92.3,
    "index": 11
  },
  {
    "date": "2025-01-20",
    "open": 95.0,
    "close": 95.42,
    "high": 96.37,
    "low": 93.34,
    "index": 12
  },
  {
    "date": "2025-01-21",
    "open": 95.51,
    "close": 104.96,
    "high": 104.96,
    "low": 94.15,
    "index": 13
  },
  {
    "date": "2025-01-22",
    "open": 104.0,
    "close": 100.72,
    "high": 104.76,
    "low": 99.43,
    "index": 14
  },
  {
    "date": "2025-01-23",
    "open": 100.8,
    "close": 97.62,
    "high": 103.5,
    "low": 96.97,
    "index": 15
  },
  {
    "date": "2025-01-24",
    "open": 95.8,
    "close": 100.87,
    "high": 101.49,
    "low": 94.9,
    "index": 16
  },
  {
    "date": "2025-01-27",
    "open": 100.8,
    "close": 93.25,
    "high": 100.86,
    "low": 92.91,
    "index": 17
  },
  {
    "date": "2025-02-05",
    "open": 96.16,
    "close": 100.98,
    "high": 102.58,
    "low": 96.16,
    "index": 18
  },
  {
    "date": "2025-02-06",
    "open": 99.99,
    "close": 111.08,
    "high": 111.08,
    "low": 99.88,
    "index": 19
  },
  {
    "date": "2025-02-07",
    "open": 110.0,
    "close": 111.73,
    "high": 115.13,
    "low": 108.0,
    "index": 20
  },
  {
    "date": "2025-02-10",
    "open": 109.22,
    "close": 113.82,
    "high": 116.13,
    "low": 107.33,
    "index": 21
  },
  {
    "date": "2025-02-11",
    "open": 111.46,
    "close": 118.29,
    "high": 122.68,
    "low": 111.0,
    "index": 22
  },
  {
    "date": "2025-02-12",
    "open": 116.86,
    "close": 129.98,
    "high": 130.12,
    "low": 115.5,
    "index": 23
  },
  {
    "date": "2025-02-13",
    "open": 126.08,
    "close": 116.98,
    "high": 127.42,
    "low": 116.98,
    "index": 24
  },
  {
    "date": "2025-02-14",
    "open": 113.0,
    "close": 113.2,
    "high": 116.98,
    "low": 112.0,
    "index": 25
  },
  {
    "date": "2025-02-17",
    "open": 112.0,
    "close": 120.32,
    "high": 121.42,
    "low": 112.0,
    "index": 26
  },
  {
    "date": "2025-02-18",
    "open": 118.04,
    "close": 116.6,
    "high": 122.57,
    "low": 115.16,
    "index": 27
  },
  {
    "date": "2025-02-19",
    "open": 115.0,
    "close": 126.06,
    "high": 127.0,
    "low": 114.8,
    "index": 28
  },
  {
    "date": "2025-02-20",
    "open": 125.58,
    "close": 131.46,
    "high": 137.0,
    "low": 122.0,
    "index": 29
  },
  {
    "date": "2025-02-21",
    "open": 128.0,
    "close": 132.6,
    "high": 135.25,
    "low": 127.6,
    "index": 30
  },
  {
    "date": "2025-02-24",
    "open": 130.09,
    "close": 130.74,
    "high": 138.18,
    "low": 123.0,
    "index": 31
  },
  {
    "date": "2025-02-25",
    "open": 126.0,
    "close": 143.58,
    "high": 143.81,
    "low": 126.0,
    "index": 32
  },
  {
    "date": "2025-02-26",
    "open": 141.71,
    "close": 157.94,
    "high": 157.94,
    "low": 141.33,
    "index": 33
  },
  {
    "date": "2025-02-27",
    "open": 156.98,
    "close": 157.0,
    "high": 163.99,
    "low": 150.01,
    "index": 34
  },
  {
    "date": "2025-02-28",
    "open": 155.0,
    "close": 141.3,
    "high": 155.39,
    "low": 141.3,
    "index": 35
  },
  {
    "date": "2025-03-03",
    "open": 140.0,
    "close": 134.45,
    "high": 141.22,
    "low": 132.92,
    "index": 36
  },
  {
    "date": "2025-03-04",
    "open": 135.99,
    "close": 147.8,
    "high": 147.9,
    "low": 135.93,
    "index": 37
  },
  {
    "date": "2025-03-05",
    "open": 146.03,
    "close": 162.58,
    "high": 162.58,
    "low": 144.31,
    "index": 38
  },
  {
    "date": "2025-03-06",
    "open": 162.3,
    "close": 160.2,
    "high": 168.77,
    "low": 158.1,
    "index": 39
  },
  {
    "date": "2025-03-07",
    "open": 160.25,
    "close": 158.8,
    "high": 165.0,
    "low": 156.26,
    "index": 40
  },
  {
    "date": "2025-03-10",
    "open": 160.0,
    "close": 161.17,
    "high": 164.95,
    "low": 155.3,
    "index": 41
  },
  {
    "date": "2025-03-11",
    "open": 157.0,
    "close": 157.1,
    "high": 162.29,
    "low": 150.0,
    "index": 42
  },
  {
    "date": "2025-03-12",
    "open": 156.21,
    "close": 156.16,
    "high": 158.29,
    "low": 154.5,
    "index": 43
  },
  {
    "date": "2025-03-13",
    "open": 154.31,
    "close": 144.63,
    "high": 155.15,
    "low": 142.88,
    "index": 44
  },
  {
    "date": "2025-03-14",
    "open": 147.5,
    "close": 147.66,
    "high": 150.27,
    "low": 143.1,
    "index": 45
  },
  {
    "date": "2025-03-17",
    "open": 146.0,
    "close": 149.65,
    "high": 152.02,
    "low": 143.15,
    "index": 46
  },
  {
    "date": "2025-03-18",
    "open": 148.83,
    "close": 145.95,
    "high": 150.61,
    "low": 144.01,
    "index": 47
  },
  {
    "date": "2025-03-19",
    "open": 144.55,
    "close": 142.91,
    "high": 146.93,
    "low": 142.2,
    "index": 48
  },
  {
    "date": "2025-03-20",
    "open": 142.6,
    "close": 144.61,
    "high": 149.7,
    "low": 140.74,
    "index": 49
  },
  {
    "date": "2025-03-21",
    "open": 141.87,
    "close": 135.25,
    "high": 142.74,
    "low": 133.06,
    "index": 50
  },
  {
    "date": "2025-03-24",
    "open": 135.25,
    "close": 134.59,
    "high": 137.1,
    "low": 130.39,
    "index": 51
  },
  {
    "date": "2025-03-25",
    "open": 135.0,
    "close": 131.14,
    "high": 137.98,
    "low": 130.89,
    "index": 52
  },
  {
    "date": "2025-03-26",
    "open": 130.7,
    "close": 132.55,
    "high": 137.23,
    "low": 130.54,
    "index": 53
  },
  {
    "date": "2025-03-27",
    "open": 132.66,
    "close": 130.79,
    "high": 134.7,
    "low": 130.6,
    "index": 54
  },
  {
    "date": "2025-03-28",
    "open": 129.51,
    "close": 130.73,
    "high": 133.17,
    "low": 127.9,
    "index": 55
  },
  {
    "date": "2025-03-31",
    "open": 127.0,
    "close": 127.83,
    "high": 129.0,
    "low": 121.83,
    "index": 56
  },
  {
    "date": "2025-04-01",
    "open": 128.88,
    "close": 126.55,
    "high": 129.76,
    "low": 124.32,
    "index": 57
  },
  {
    "date": "2025-04-02",
    "open": 125.88,
    "close": 126.7,
    "high": 129.28,
    "low": 125.28,
    "index": 58
  },
  {
    "date": "2025-04-03",
    "open": 124.7,
    "close": 122.2,
    "high": 127.5,
    "low": 121.72,
    "index": 59
  },
  {
    "date": "2025-04-07",
    "open": 109.98,
    "close": 109.98,
    "high": 109.98,
    "low": 109.98,
    "index": 60
  },
  {
    "date": "2025-04-08",
    "open": 98.98,
    "close": 98.98,
    "high": 103.5,
    "low": 98.98,
    "index": 61
  },
  {
    "date": "2025-04-09",
    "open": 93.49,
    "close": 99.0,
    "high": 103.0,
    "low": 89.08,
    "index": 62
  },
  {
    "date": "2025-04-10",
    "open": 104.0,
    "close": 103.22,
    "high": 107.28,
    "low": 101.0,
    "index": 63
  },
  {
    "date": "2025-04-11",
    "open": 101.07,
    "close": 113.54,
    "high": 113.54,
    "low": 100.11,
    "index": 64
  },
  {
    "date": "2025-04-14",
    "open": 116.88,
    "close": 114.25,
    "high": 118.72,
    "low": 113.5,
    "index": 65
  },
  {
    "date": "2025-04-15",
    "open": 115.0,
    "close": 113.41,
    "high": 116.29,
    "low": 111.7,
    "index": 66
  },
  {
    "date": "2025-04-16",
    "open": 112.51,
    "close": 112.77,
    "high": 116.47,
    "low": 111.6,
    "index": 67
  },
  {
    "date": "2025-04-17",
    "open": 111.61,
    "close": 111.33,
    "high": 115.86,
    "low": 111.08,
    "index": 68
  },
  {
    "date": "2025-04-18",
    "open": 111.11,
    "close": 109.55,
    "high": 112.39,
    "low": 108.7,
    "index": 69
  },
  {
    "date": "2025-04-21",
    "open": 108.11,
    "close": 116.0,
    "high": 116.83,
    "low": 106.69,
    "index": 70
  },
  {
    "date": "2025-04-22",
    "open": 116.0,
    "close": 113.99,
    "high": 118.1,
    "low": 113.91,
    "index": 71
  },
  {
    "date": "2025-04-23",
    "open": 117.76,
    "close": 125.39,
    "high": 125.39,
    "low": 116.51,
    "index": 72
  },
  {
    "date": "2025-04-24",
    "open": 127.5,
    "close": 125.32,
    "high": 131.48,
    "low": 124.62,
    "index": 73
  },
  {
    "date": "2025-04-25",
    "open": 127.49,
    "close": 127.35,
    "high": 130.68,
    "low": 123.44,
    "index": 74
  },
  {
    "date": "2025-04-28",
    "open": 127.35,
    "close": 123.54,
    "high": 128.98,
    "low": 123.25,
    "index": 75
  },
  {
    "date": "2025-04-29",
    "open": 122.11,
    "close": 125.0,
    "high": 127.39,
    "low": 120.01,
    "index": 76
  },
  {
    "date": "2025-04-30",
    "open": 124.57,
    "close": 127.06,
    "high": 128.97,
    "low": 123.08,
    "index": 77
  },
  {
    "date": "2025-05-06",
    "open": 128.97,
    "close": 130.51,
    "high": 132.74,
    "low": 126.0,
    "index": 78
  },
  {
    "date": "2025-05-07",
    "open": 132.14,
    "close": 128.05,
    "high": 132.62,
    "low": 125.25,
    "index": 79
  },
  {
    "date": "2025-05-08",
    "open": 126.81,
    "close": 126.99,
    "high": 129.0,
    "low": 125.67,
    "index": 80
  },
  {
    "date": "2025-05-09",
    "open": 127.0,
    "close": 124.0,
    "high": 127.0,
    "low": 122.22,
    "index": 81
  },
  {
    "date": "2025-05-12",
    "open": 126.0,
    "close": 133.5,
    "high": 135.98,
    "low": 125.0,
    "index": 82
  },
  {
    "date": "2025-05-13",
    "open": 130.0,
    "close": 125.6,
    "high": 131.0,
    "low": 125.0,
    "index": 83
  },
  {
    "date": "2025-05-14",
    "open": 124.36,
    "close": 124.64,
    "high": 125.8,
    "low": 123.0,
    "index": 84
  },
  {
    "date": "2025-05-15",
    "open": 124.2,
    "close": 120.13,
    "high": 124.2,
    "low": 120.0,
    "index": 85
  },
  {
    "date": "2025-05-16",
    "open": 118.97,
    "close": 120.92,
    "high": 123.34,
    "low": 118.2,
    "index": 86
  },
  {
    "date": "2025-05-19",
    "open": 119.71,
    "close": 115.35,
    "high": 120.0,
    "low": 113.5,
    "index": 87
  },
  {
    "date": "2025-05-20",
    "open": 115.99,
    "close": 115.05,
    "high": 116.37,
    "low": 114.13,
    "index": 88
  },
  {
    "date": "2025-05-21",
    "open": 114.15,
    "close": 113.77,
    "high": 115.38,
    "low": 113.01,
    "index": 89
  },
  {
    "date": "2025-05-22",
    "open": 113.74,
    "close": 112.6,
    "high": 114.89,
    "low": 112.11,
    "index": 90
  },
  {
    "date": "2025-05-23",
    "open": 111.8,
    "close": 111.74,
    "high": 114.8,
    "low": 111.21,
    "index": 91
  },
  {
    "date": "2025-05-26",
    "open": 111.12,
    "close": 110.85,
    "high": 112.3,
    "low": 110.0,
    "index": 92
  },
  {
    "date": "2025-05-27",
    "open": 110.9,
    "close": 107.72,
    "high": 111.26,
    "low": 107.31,
    "index": 93
  },
  {
    "date": "2025-05-28",
    "open": 107.58,
    "close": 106.62,
    "high": 108.89,
    "low": 106.16,
    "index": 94
  },
  {
    "date": "2025-05-29",
    "open": 106.65,
    "close": 107.28,
    "high": 108.5,
    "low": 106.65,
    "index": 95
  },
  {
    "date": "2025-05-30",
    "open": 107.0,
    "close": 103.06,
    "high": 107.0,
    "low": 102.68,
    "index": 96
  },
  {
    "date": "2025-06-03",
    "open": 102.0,
    "close": 102.9,
    "high": 104.38,
    "low": 101.1,
    "index": 97
  },
  {
    "date": "2025-06-04",
    "open": 102.9,
    "close": 103.4,
    "high": 103.92,
    "low": 101.8,
    "index": 98
  },
  {
    "date": "2025-06-05",
    "open": 103.4,
    "close": 104.31,
    "high": 105.7,
    "low": 102.33,
    "index": 99
  },
  {
    "date": "2025-06-06",
    "open": 104.66,
    "close": 103.09,
    "high": 104.69,
    "low": 102.07,
    "index": 100
  },
  {
    "date": "2025-06-09",
    "open": 103.87,
    "close": 104.5,
    "high": 109.3,
    "low": 103.82,
    "index": 101
  },
  {
    "date": "2025-06-10",
    "open": 104.5,
    "close": 101.23,
    "high": 105.0,
    "low": 98.58,
    "index": 102
  },
  {
    "date": "2025-06-11",
    "open": 101.22,
    "close": 101.96,
    "high": 102.5,
    "low": 100.23,
    "index": 103
  },
  {
    "date": "2025-06-12",
    "open": 101.01,
    "close": 102.08,
    "high": 102.37,
    "low": 100.6,
    "index": 104
  },
  {
    "date": "2025-06-13",
    "open": 101.01,
    "close": 100.09,
    "high": 103.69,
    "low": 99.49,
    "index": 105
  },
  {
    "date": "2025-06-16",
    "open": 99.42,
    "close": 99.53,
    "high": 100.48,
    "low": 99.36,
    "index": 106
  },
  {
    "date": "2025-06-17",
    "open": 99.98,
    "close": 99.01,
    "high": 100.8,
    "low": 98.26,
    "index": 107
  },
  {
    "date": "2025-06-18",
    "open": 98.78,
    "close": 100.22,
    "high": 101.0,
    "low": 98.33,
    "index": 108
  },
  {
    "date": "2025-06-19",
    "open": 100.05,
    "close": 103.08,
    "high": 105.84,
    "low": 99.11,
    "index": 109
  },
  {
    "date": "2025-06-20",
    "open": 102.62,
    "close": 98.4,
    "high": 102.62,
    "low": 98.3,
    "index": 110
  },
  {
    "date": "2025-06-23",
    "open": 96.0,
    "close": 98.12,
    "high": 99.17,
    "low": 95.59,
    "index": 111
  },
  {
    "date": "2025-06-24",
    "open": 99.2,
    "close": 105.24,
    "high": 106.5,
    "low": 99.02,
    "index": 112
  }
];
        const windowSize = 20;

        // 当前状态
        let currentIndex = 0;
        let calculator = new StockTrendCalculator(windowSize);
        let autoPlayInterval = null;

        // 图表实例
        let chart = echarts.init(document.getElementById('chart'));

        // 更新图表
        function updateChart() {
            if (currentIndex === 0) {
                chart.clear();
                return;
            }

            // 获取当前显示的数据
            const currentData = stockData.slice(0, currentIndex);
            const dates = currentData.map(d => d.date);
            const klineData = currentData.map(d => [d.open, d.close, d.low, d.high]);

            // 计算趋势线
            const [highLine, lowLine] = calculator.getCurrentTrendLines();
            const dayIndices = currentData.map((_, i) => i);

            let highTrends = [];
            let lowTrends = [];
            let highSlope = 0, lowSlope = 0;

            if (highLine && lowLine) {
                highTrends = calculator.getTrendLineValues(highLine, dayIndices);
                lowTrends = calculator.getTrendLineValues(lowLine, dayIndices);
                highSlope = highLine.slope;
                lowSlope = lowLine.slope;
            }

            // 更新统计信息
            document.getElementById('currentDay').textContent = currentIndex;
            document.getElementById('highSlope').textContent = highSlope.toFixed(6);
            document.getElementById('lowSlope').textContent = lowSlope.toFixed(6);

            // 图表配置
            const option = {
                title: {
                    text: '交互式股票趋势分析',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['K线', '高点趋势线', '低点趋势线'],
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: dates,
                    scale: true,
                    boundaryGap: false,
                    axisLine: { onZero: false },
                    splitLine: { show: false },
                    min: 'dataMin',
                    max: 'dataMax'
                },
                yAxis: {
                    scale: true,
                    splitArea: {
                        show: true
                    }
                },
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    },
                    {
                        show: true,
                        type: 'slider',
                        top: '90%',
                        start: 0,
                        end: 100
                    }
                ],
                series: [
                    {
                        name: 'K线',
                        type: 'candlestick',
                        data: klineData,
                        itemStyle: {
                            color: '#ec0000',
                            color0: '#00da3c',
                            borderColor: '#8A0000',
                            borderColor0: '#008F28'
                        }
                    },
                    {
                        name: '高点趋势线',
                        type: 'line',
                        data: highTrends,
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: '#1E90FF',
                            width: 3
                        }
                    },
                    {
                        name: '低点趋势线',
                        type: 'line',
                        data: lowTrends,
                        smooth: false,
                        symbol: 'none',
                        lineStyle: {
                            color: '#FF8C00',
                            width: 3
                        }
                    }
                ]
            };

            chart.setOption(option);
        }

        // 添加下一个交易日
        function addNextDay() {
            if (currentIndex >= stockData.length) {
                document.getElementById('info').textContent = '所有数据已添加完毕！';
                document.getElementById('nextBtn').disabled = true;
                return;
            }

            const record = stockData[currentIndex];
            calculator.addRecord(record.date, record.high, record.low);
            currentIndex++;

            updateChart();

            document.getElementById('info').textContent =
                `已添加第 ${currentIndex} 个交易日: ${record.date}, 高: ${record.high}, 低: ${record.low}`;

            if (currentIndex >= stockData.length) {
                document.getElementById('nextBtn').disabled = true;
                document.getElementById('info').textContent += ' - 所有数据已添加完毕！';
            }
        }

        // 重置图表
        function resetChart() {
            currentIndex = 0;
            calculator = new StockTrendCalculator(windowSize);
            chart.clear();
            document.getElementById('nextBtn').disabled = false;
            document.getElementById('currentDay').textContent = '0';
            document.getElementById('highSlope').textContent = '--';
            document.getElementById('lowSlope').textContent = '--';
            document.getElementById('info').textContent = '图表已重置，点击"添加下一个交易日"开始分析';
            stopAuto();
        }

        // 自动播放
        function autoPlay() {
            if (autoPlayInterval) return;

            document.getElementById('autoBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;

            autoPlayInterval = setInterval(() => {
                if (currentIndex >= stockData.length) {
                    stopAuto();
                    return;
                }
                addNextDay();
            }, 500); // 每500ms添加一个数据点
        }

        // 停止自动播放
        function stopAuto() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
            }
            document.getElementById('autoBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
        }

        // 初始化图表
        updateChart();
    </script>
</body>
</html>
    