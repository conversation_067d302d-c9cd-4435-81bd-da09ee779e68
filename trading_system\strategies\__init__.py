"""
策略模块

提供交易策略的基础框架和具体实现。
所有策略都基于事件驱动模型，监听市场事件并产生交易信号。
"""

from .base_strategy import BaseStrategy
from .example_strategy import TurnPointStrategy
from .enhanced_turn_point_strategy import EnhancedTurnPointStrategy
from .fixed_enhanced_strategy import FixedEnhancedTurnPointStrategy
from .simple_trend_strategy import SimpleTrendStrategy

__all__ = [
    'BaseStrategy', 'TurnPointStrategy', 'EnhancedTurnPointStrategy', 'FixedEnhancedTurnPointStrategy', 'SimpleTrendStrategy'
]
