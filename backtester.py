import pandas as pd


class Backtester:

    def __init__(self, initial_capital=100000):
        self.initial_capital = initial_capital
        self.reset()
    
    def reset(self):
        self.capital = self.initial_capital
        self.position = 0
        self.portfolio_value = self.initial_capital
        self.portfolio_history = []
        self.trade_history = []
    
    def execute_trade(self, signal, current_price):
        """执行交易信号"""
        # 简单固定比例交易
        trade_percent = 0.2  # 每次交易资金的20%
        trade_amount = self.capital * trade_percent
        
        if signal['type'] == 'BUY' and trade_amount >= current_price:
            # 计算可买数量
            qty = int(trade_amount / current_price)
            cost = qty * current_price
            
            if cost <= self.capital:
                self.position += qty
                self.capital -= cost
                
                self.trade_history.append({
                    'date': signal['date'],
                    'type': 'BUY',
                    'price': current_price,
                    'quantity': qty,
                    'cost': cost,
                    'reason': signal['reason']
                })
                return True
                
        elif signal['type'] == 'SELL' and self.position > 0:
            # 卖出持仓的20%
            qty = max(1, int(self.position * trade_percent))
            revenue = qty * current_price
            
            self.position -= qty
            self.capital += revenue
            
            self.trade_history.append({
                'date': signal['date'],
                'type': 'SELL',
                'price': current_price,
                'quantity': qty,
                'revenue': revenue,
                'reason': signal['reason']
            })
            return True
        
        return False
    
    def run_backtest(self, events):
        """运行回测"""
        self.reset()
        
        for event in events:
            # 更新投资组合价值
            position_value = self.position * event.close
            self.portfolio_value = self.capital + position_value
            self.portfolio_history.append({
                'date': event.date,
                'value': self.portfolio_value,
                'cash': self.capital,
                'position': self.position,
                'price': event.close
            })
            
            # 执行当日的交易信号
            for signal in event.signals:
                self.execute_trade(signal, event.close)
        
        return self._generate_report()
    
    def _generate_report(self):
        """生成回测报告"""
        initial = self.initial_capital
        final = self.portfolio_value
        profit = final - initial
        return_pct = (profit / initial) * 100 if initial > 0 else 0
        
        winning_trades = [t for t in self.trade_history if t['type'] == 'SELL' and 
                         t['revenue'] > t.get('cost', t['price'] * t['quantity'])]
        
        losing_trades = [t for t in self.trade_history if t['type'] == 'SELL' and 
                        t['revenue'] <= t.get('cost', t['price'] * t['quantity'])]
        
        win_rate = len(winning_trades) / len(self.trade_history) * 100 if self.trade_history else 0
        
        return {
            'initial_capital': initial,
            'final_value': final,
            'profit': profit,
            'return_percent': return_pct,
            'num_trades': len(self.trade_history),
            'win_rate': win_rate,
            'trade_history': self.trade_history,
            'portfolio_history': pd.DataFrame(self.portfolio_history),
            'max_drawdown': self._calculate_max_drawdown()
        }
    
    def _calculate_max_drawdown(self):
        """计算最大回撤"""
        if not self.portfolio_history:
            return 0
            
        values = [p['value'] for p in self.portfolio_history]
        peak = values[0]
        max_drawdown = 0
        
        for value in values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        return max_drawdown