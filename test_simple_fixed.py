#!/usr/bin/env python3
"""
测试简单趋势策略 - 修复版

验证交易是否分布在不同日期
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
from typing import Dict, Any

from trading_system.core import EventEngine, MarketEvent, SignalEvent, OrderEvent, FillEvent
from trading_system.data import HistoricalDataProvider
from trading_system.analysis import IndicatorCalculator
from trading_system.strategies import SimpleTrendStrategy
from trading_system.execution import BacktestExecutionHandler
from trading_system.portfolio import PortfolioManager


def main():
    """主函数"""
    print("=" * 100)
    print("简单趋势策略测试 - 修复版")
    print("验证交易是否分布在不同日期")
    print("=" * 100)
    
    try:
        # 配置
        config = {
            'symbol': '003021',
            'start_date': '20200101',
            'end_date': '20250630',
            'initial_cash': 100000,
            'slippage_pct': 0.001,
        }
        
        print(f"交易标的: {config['symbol']}")
        print(f"回测期间: {config['start_date']} - {config['end_date']}")
        print(f"初始资金: ¥{config['initial_cash']:,}")
        print(f"策略类型: 简单趋势策略 (基于MA20和RSI)")
        print("-" * 100)
        
        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        logger = logging.getLogger(__name__)
        
        # 创建组件
        event_engine = EventEngine()
        data_provider = HistoricalDataProvider(
            symbol=config['symbol'],
            start_date=config['start_date'],
            end_date=config['end_date']
        )
        indicator_calculator = IndicatorCalculator()
        strategy = SimpleTrendStrategy(config['symbol'])
        execution_handler = BacktestExecutionHandler(
            event_engine=event_engine,
            slippage_pct=config['slippage_pct']
        )
        portfolio_manager = PortfolioManager(initial_cash=config['initial_cash'])
        
        # 交易记录
        trade_records = []
        
        def on_market_event(event: MarketEvent):
            """处理市场数据事件"""
            # 计算技术指标
            indicators = indicator_calculator.calculate_all(event)
            event.indicators.update(indicators)
            
            # 策略处理
            signals = strategy.on_market_event(event)
            for signal in signals:
                event_engine.put_event(signal)
            
            # 更新执行处理器的市场数据
            execution_handler.update_market_data(event)
            
            # 更新投资组合市值
            portfolio_manager.update_market_value(event)
        
        def on_signal_event(event: SignalEvent):
            """处理交易信号事件"""
            orders = execution_handler.process_signal(event)
            for order in orders:
                event_engine.put_event(order)
        
        def on_order_event(event: OrderEvent):
            """处理订单事件"""
            fill_event = execution_handler.execute_order(event)
            if fill_event:
                event_engine.put_event(fill_event)
        
        def on_fill_event(event: FillEvent):
            """处理成交事件"""
            # 更新策略状态
            strategy.on_fill_event(event)
            
            # 更新投资组合
            portfolio_manager.process_fill_event(event)
            
            # 记录交易
            trade_records.append({
                'timestamp': event.timestamp,
                'direction': event.direction,
                'quantity': event.quantity,
                'price': event.fill_price,
                'date': event.timestamp.strftime('%Y-%m-%d')
            })
            
            logger.info(f"Trade: {event.direction} {event.quantity} @ ¥{event.fill_price:.2f} on {event.timestamp.strftime('%Y-%m-%d')}")
        
        # 注册事件处理器
        event_engine.register_handler(MarketEvent, on_market_event)
        event_engine.register_handler(SignalEvent, on_signal_event)
        event_engine.register_handler(OrderEvent, on_order_event)
        event_engine.register_handler(FillEvent, on_fill_event)
        
        logger.info("System initialized successfully")
        
        # 运行回测
        logger.info("Starting backtest...")
        event_engine.start()
        
        event_count = 0
        for market_event in data_provider.get_data_stream():
            event_engine.put_event(market_event)
            event_count += 1
            
            if event_count % 200 == 0:
                logger.info(f"已处理 {event_count} 条 market events")
        
        # 等待所有事件处理完成
        event_engine.wait_for_completion(timeout=10)
        event_engine.stop()
        
        logger.info(f"回测结束. 处理完成 {event_count} 条 market events")
        
        # 分析结果
        print("\n" + "=" * 80)
        print("交易结果分析")
        print("=" * 80)
        
        if not trade_records:
            print("❌ 没有生成任何交易记录")
            return
        
        # 统计交易日期分布
        trade_dates = {}
        for trade in trade_records:
            date = trade['date']
            if date not in trade_dates:
                trade_dates[date] = []
            trade_dates[date].append(trade)
        
        print(f"📊 总交易笔数: {len(trade_records)}")
        print(f"📅 交易日期数: {len(trade_dates)}")
        print(f"💰 最终资金: ¥{portfolio_manager.get_portfolio_value():,.2f}")
        print(f"📈 总收益率: {(portfolio_manager.get_portfolio_value() - config['initial_cash']) / config['initial_cash'] * 100:.2f}%")
        
        print(f"\n📅 交易日期分布:")
        sorted_dates = sorted(trade_dates.keys())
        for i, date in enumerate(sorted_dates, 1):
            trades_on_date = trade_dates[date]
            print(f"  {i:2d}. {date}: {len(trades_on_date)} 笔交易")
            for trade in trades_on_date:
                print(f"      {trade['direction']} {trade['quantity']} @ ¥{trade['price']:.2f}")
        
        # 验证结果
        if len(trade_dates) > 1:
            print(f"\n✅ 成功！交易分布在 {len(trade_dates)} 个不同日期")
        else:
            print(f"\n❌ 失败！所有交易仍然集中在同一天: {list(trade_dates.keys())[0]}")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 系统运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
