"""
示例策略实现

基于转折点和技术指标的交易策略，迁移自原有的signal_gen.py逻辑。
"""

from typing import List, Optional
from datetime import datetime

from .base_strategy import BaseStrategy
from ..core.event import MarketEvent, SignalEvent, TurnPointEvent


class TurnPointStrategy(BaseStrategy):
    """
    转折点策略
    
    基于价格转折点和技术指标生成交易信号。
    结合RSI超买超卖、均线交叉等多种信号源。
    """
    
    def __init__(self, symbol: str):
        super().__init__("TurnPointStrategy", symbol)
        
        # 设置默认参数
        self.set_parameter('rsi_oversold', 30)
        self.set_parameter('rsi_overbought', 70)
        self.set_parameter('use_turn_points', True)
        self.set_parameter('use_rsi_signals', True)
        self.set_parameter('use_ma_cross', True)
        self.set_parameter('position_size', 100)
        
        # 策略状态
        self.last_ma5 = None
        self.last_ma20 = None
    
    def on_market_event(self, event: MarketEvent) -> List[SignalEvent]:
        """
        处理市场数据事件
        
        Args:
            event: 市场数据事件
            
        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
        signals = []
        
        # 基于技术指标生成信号
        if self.get_parameter('use_rsi_signals'):
            rsi_signals = self._generate_rsi_signals(event)
            signals.extend(rsi_signals)
        
        if self.get_parameter('use_ma_cross'):
            ma_signals = self._generate_ma_cross_signals(event)
            signals.extend(ma_signals)
        
        return signals
    
    def on_turn_point_event(self, event: TurnPointEvent) -> List[SignalEvent]:
        """
        处理转折点事件
        
        Args:
            event: 转折点事件
            
        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
        if not self.get_parameter('use_turn_points'):
            return []
        
        signals = []
        position_size = self.get_parameter('position_size')
        
        if event.point_type == 'TROUGH':
            # 波谷 - 买入信号
            signal = self._create_signal(
                signal_type='BUY',
                quantity=position_size,
                price=event.price,
                reason=f'Trough detected at {event.price:.2f}, kept for {event.keep_days} days',
                confidence=0.8
            )
            signals.append(signal)
            
            self.logger.info(f"BUY signal @ {event.timestamp}: Trough detected ({event.price:.2f})")
        
        elif event.point_type == 'PEAK':
            # 波峰 - 卖出信号
            signal = self._create_signal(
                signal_type='SELL',
                quantity=position_size,
                price=event.price,
                reason=f'Peak detected at {event.price:.2f}, kept for {event.keep_days} days',
                confidence=0.8
            )
            signals.append(signal)
            
            self.logger.info(f"SELL signal @ {event.timestamp}: Peak detected ({event.price:.2f})")
        
        return signals
    
    def _generate_rsi_signals(self, event: MarketEvent) -> List[SignalEvent]:
        """
        基于RSI生成交易信号
        
        Args:
            event: 市场数据事件
            
        Returns:
            List[SignalEvent]: RSI信号列表
        """
        signals = []
        rsi = event.indicators.get('RSI')
        
        if rsi is None:
            return signals
        
        position_size = self.get_parameter('position_size')
        rsi_oversold = self.get_parameter('rsi_oversold')
        rsi_overbought = self.get_parameter('rsi_overbought')
        
        if rsi < rsi_oversold:
            # RSI超卖 - 买入信号
            signal = self._create_signal(
                signal_type='BUY',
                quantity=position_size,
                price=event.close,
                reason=f'RSI oversold ({rsi:.2f})',
                confidence=0.6
            )
            signals.append(signal)
            
            self.logger.info(f"BUY signal @ {event.timestamp}: RSI oversold ({rsi:.2f})")
        
        elif rsi > rsi_overbought:
            # RSI超买 - 卖出信号
            signal = self._create_signal(
                signal_type='SELL',
                quantity=position_size,
                price=event.close,
                reason=f'RSI overbought ({rsi:.2f})',
                confidence=0.6
            )
            signals.append(signal)
            
            self.logger.info(f"SELL signal @ {event.timestamp}: RSI overbought ({rsi:.2f})")
        
        return signals
    
    def _generate_ma_cross_signals(self, event: MarketEvent) -> List[SignalEvent]:
        """
        基于均线交叉生成交易信号
        
        Args:
            event: 市场数据事件
            
        Returns:
            List[SignalEvent]: 均线交叉信号列表
        """
        signals = []
        ma5 = event.indicators.get('MA5')
        ma20 = event.indicators.get('MA20')
        
        if ma5 is None or ma20 is None:
            return signals
        
        # 需要有历史数据才能判断交叉
        if self.last_ma5 is not None and self.last_ma20 is not None:
            position_size = self.get_parameter('position_size')
            
            # 金叉：MA5从下方穿越MA20
            if (self.last_ma5 <= self.last_ma20 and ma5 > ma20):
                signal = self._create_signal(
                    signal_type='BUY',
                    quantity=position_size,
                    price=event.close,
                    reason=f'Golden Cross (MA5={ma5:.2f} > MA20={ma20:.2f})',
                    confidence=0.7
                )
                signals.append(signal)
                
                self.logger.info(f"BUY signal @ {event.timestamp}: Golden Cross (MA5={ma5:.2f} > MA20={ma20:.2f})")
            
            # 死叉：MA5从上方跌破MA20
            elif (self.last_ma5 >= self.last_ma20 and ma5 < ma20):
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=position_size,
                    price=event.close,
                    reason=f'Death Cross (MA5={ma5:.2f} < MA20={ma20:.2f})',
                    confidence=0.7
                )
                signals.append(signal)
                
                self.logger.info(f"SELL signal @ {event.timestamp}: Death Cross (MA5={ma5:.2f} < MA20={ma20:.2f})")
        
        # 更新历史数据
        self.last_ma5 = ma5
        self.last_ma20 = ma20
        
        return signals
    
    def reset(self):
        """重置策略状态"""
        super().reset()
        self.last_ma5 = None
        self.last_ma20 = None


class SimpleMovingAverageStrategy(BaseStrategy):
    """
    简单移动平均策略
    
    基于价格与移动平均线的关系生成交易信号。
    """
    
    def __init__(self, symbol: str, ma_period: int = 20):
        super().__init__("SimpleMAStrategy", symbol)
        self.set_parameter('ma_period', ma_period)
        self.set_parameter('position_size', 100)
        
        self.last_price = None
        self.last_ma = None
    
    def on_market_event(self, event: MarketEvent) -> List[SignalEvent]:
        """
        处理市场数据事件
        
        Args:
            event: 市场数据事件
            
        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
        signals = []
        ma_period = self.get_parameter('ma_period')
        ma_key = f'MA{ma_period}'
        ma = event.indicators.get(ma_key)
        
        if ma is None:
            return signals
        
        current_price = event.close
        
        # 需要有历史数据才能判断穿越
        if self.last_price is not None and self.last_ma is not None:
            position_size = self.get_parameter('position_size')
            
            # 价格从下方穿越均线 - 买入
            if self.last_price <= self.last_ma and current_price > ma:
                signal = self._create_signal(
                    signal_type='BUY',
                    quantity=position_size,
                    price=current_price,
                    reason=f'Price crossed above MA{ma_period} ({ma:.2f})',
                    confidence=0.6
                )
                signals.append(signal)
            
            # 价格从上方跌破均线 - 卖出
            elif self.last_price >= self.last_ma and current_price < ma:
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=position_size,
                    price=current_price,
                    reason=f'Price crossed below MA{ma_period} ({ma:.2f})',
                    confidence=0.6
                )
                signals.append(signal)
        
        # 更新历史数据
        self.last_price = current_price
        self.last_ma = ma
        
        return signals
