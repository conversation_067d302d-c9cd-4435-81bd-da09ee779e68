# 交易系统架构设计方案 (v2 - 事件驱动模型)

## 1. 核心设计思想

本架构旨在构建一个健壮、可扩展、易于测试的量化交易系统。其核心是**事件驱动模型**，系统中的所有组件都是相互解耦的，它们通过一个中央事件总线进行异步通信。

该设计严格遵守**无未来数据**原则，通过流式处理数据，确保无论是历史回测还是实盘交易，其底层逻辑都保持一致，从而实现平滑过渡。

## 2. 模块化目录结构

```
trading_system/
├── main.py                     # 系统配置器和启动器
│
├── core/
│   ├── __init__.py
│   ├── event.py                # 定义所有事件类型 (MarketEvent, SignalEvent, OrderEvent, FillEvent)
│   └── engine.py               # 事件驱动引擎，管理事件队列和主循环
│
├── data/
│   ├── __init__.py
│   └── provider.py             # 数据提供者，能以“流”的形式产生数据
│
├── analysis/
│   ├── __init__.py
│   ├── indicators.py           # 状态化的指标计算器 (适配流式数据)
│   └── turn_point_detector.py  # 转折点检测器 (状态化)
│
├── strategies/
│   ├── __init__.py
│   ├── base_strategy.py        # 策略基类
│   └── example_strategy.py     # 具体策略实现，监听事件并产生信号
│
├── execution/
│   ├── __init__.py
│   ├── base_handler.py         # 执行处理器基类
│   └── backtest_handler.py     # 模拟交易所，处理订单并产生“成交”回报
│
├── portfolio/
│   ├── __init__.py
│   └── manager.py              # 投资组合管理器，根据“成交”回报管理资金和持仓
│
└── utils/
    ├── __init__.py
    └── visualization.py        # 可视化工具
```

## 3. 组件职责详解

### 3.1 `core` - 核心引擎
- **`event.py`**: 定义系统的“语言”。包含所有事件类，如 `MarketEvent`, `SignalEvent`, `OrderEvent`, `FillEvent`。
- **`engine.py`**: 系统的心脏。维护一个事件队列，负责事件的循环和分发。

### 3.2 `data` - 数据层
- **`provider.py`**: 唯一的数据来源。在回测模式下，通过 `yield` 逐条生成历史K线；在实盘模式下，可以对接实时行情API，产生实时K线流。

### 3.3 `analysis` - 分析层
- **`indicators.py`**: **状态化**的指标计算器。每个指标都是一个类，自己维护计算所需的小窗口数据，以处理数据流。
- **`turn_point_detector.py`**: 状态化的转折点检测器，监听市场事件并识别价格模式。

### 3.4 `strategies` - 策略层
- **`base_strategy.py`**: 定义策略接口。
- **`example_strategy.py`**: 具体的策略逻辑。监听 `MarketEvent` 和其他分析事件，产生 `SignalEvent`（交易意图）。策略本身不关心如何下单或账户状态。

### 3.5 `execution` - 执行层
- **`base_handler.py`**: 定义执行处理器接口。
- **`backtest_handler.py`**: 模拟交易所。监听 `SignalEvent`，决定成交价格和数量，然后产生 `FillEvent`（成交回报）。在实盘中，这里可以替换为对接真实券商API的处理器。

### 3.6 `portfolio` - 账户层
- **`manager.py`**: 投资组合管理器。监听 `FillEvent`，根据成交回报来更新现金、持仓、市值等账户信息。

### 3.7 `utils` - 工具层
- **`visualization.py`**: 独立的绘图和报告工具，用于展示回测结果。

## 4. 事件驱动流程 (以回测为例)

1.  **启动**: `main.py` 初始化所有组件并启动 `engine`。
2.  **数据**: `engine` 向 `data.provider` 请求数据，后者 `yield` 一条K线，`engine` 将其包装成 `MarketEvent` 推入队列。
3.  **分发**: `engine` 从队列取出 `MarketEvent` 并广播。
4.  **分析与策略**: `analysis` 和 `strategies` 组件收到 `MarketEvent`，更新自身状态，策略可能会产生 `SignalEvent` 推入队列。
5.  **执行**: `engine` 取出 `SignalEvent` 并广播。`execution.backtest_handler` 收到后，模拟成交，并产生 `FillEvent` 推入队列。
6.  **账户更新**: `engine` 取出 `FillEvent` 并广播。`portfolio.manager` 收到后，更新账户状态。
7.  **循环**: 流程回到第2步，直到数据流结束。

## 5. 架构优势
- **高内聚，低耦合**: 组件职责单一，通过事件解耦。
- **无缝切换**: 通过更换 `data` 和 `execution` 模块，可轻松从回测切换到实盘。
- **高可扩展性**: 添加新策略、新指标、新数据源都无需修改核心代码。
- **高可测试性**: 每个组件都可以被独立进行单元测试。