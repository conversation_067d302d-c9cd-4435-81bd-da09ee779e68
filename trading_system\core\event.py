"""
事件定义模块

定义系统中所有的事件类型，这些事件构成了系统组件间通信的"语言"。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime


@dataclass
class MarketEvent:
    """
    市场数据事件
    
    当新的市场数据（如K线）到达时产生此事件。
    包含OHLCV数据和计算出的技术指标。
    """
    symbol: str                                    # 股票代码
    timestamp: datetime                            # 时间戳
    open: float                                    # 开盘价
    high: float                                    # 最高价
    low: float                                     # 最低价
    close: float                                   # 收盘价
    volume: float                                  # 成交量
    indicators: Dict[str, float] = field(default_factory=dict)  # 技术指标
    metadata: Dict[str, Any] = field(default_factory=dict)      # 额外元数据


@dataclass
class SignalEvent:
    """
    交易信号事件
    
    策略产生交易意图时发出此事件。
    包含买卖方向、数量、价格等信息。
    """
    symbol: str                                    # 股票代码
    timestamp: datetime                            # 信号产生时间
    signal_type: str                              # 信号类型: 'BUY', 'SELL', 'HOLD'
    quantity: int                                 # 交易数量
    price: Optional[float] = None                 # 期望价格（None表示市价）
    reason: str = ""                              # 信号产生原因
    confidence: float = 1.0                       # 信号置信度 (0-1)
    metadata: Dict[str, Any] = field(default_factory=dict)      # 额外元数据


@dataclass
class OrderEvent:
    """
    订单事件
    
    当需要向交易所发送订单时产生此事件。
    由投资组合管理器根据SignalEvent产生。
    """
    symbol: str                                    # 股票代码
    timestamp: datetime                            # 订单时间
    order_type: str                               # 订单类型: 'MARKET', 'LIMIT', 'STOP'
    direction: str                                # 方向: 'BUY', 'SELL'
    quantity: int                                 # 订单数量
    price: Optional[float] = None                 # 订单价格（市价单为None）
    order_id: Optional[str] = None                # 订单ID
    metadata: Dict[str, Any] = field(default_factory=dict)      # 额外元数据


@dataclass
class FillEvent:
    """
    成交事件
    
    当订单被执行（成交）时产生此事件。
    包含实际成交的价格、数量、手续费等信息。
    """
    symbol: str                                    # 股票代码
    timestamp: datetime                            # 成交时间
    direction: str                                # 成交方向: 'BUY', 'SELL'
    quantity: int                                 # 成交数量
    fill_price: float                             # 成交价格
    commission: float = 0.0                       # 手续费
    order_id: Optional[str] = None                # 对应的订单ID
    fill_id: Optional[str] = None                 # 成交ID
    metadata: Dict[str, Any] = field(default_factory=dict)      # 额外元数据


@dataclass
class TurnPointEvent:
    """
    转折点事件
    
    当检测到价格转折点时产生此事件。
    用于技术分析和策略决策。
    """
    symbol: str                                    # 股票代码
    timestamp: datetime                            # 转折点时间
    point_type: str                               # 转折点类型: 'PEAK', 'TROUGH'
    price: float                                  # 转折点价格
    start_date: datetime                          # 趋势开始时间
    keep_days: int                                # 持续天数
    extent_percent: float                         # 变化幅度百分比
    volume: float = 0.0                           # 累计成交量
    metadata: Dict[str, Any] = field(default_factory=dict)      # 额外元数据


# 事件类型映射
EVENT_TYPES = {
    'MARKET': MarketEvent,
    'SIGNAL': SignalEvent,
    'ORDER': OrderEvent,
    'FILL': FillEvent,
    'TURN_POINT': TurnPointEvent,
}
