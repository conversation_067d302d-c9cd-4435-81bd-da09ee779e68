import pandas as pd
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Callable

@dataclass
class MarketEvent:
    """市场行情事件"""
    date: str
    open: float
    high: float
    low: float
    close: float
    volume: float
    indicators: Dict[str, float] = field(default_factory=dict)  # 技术指标值
    point_info: Optional[Dict] = None  # 转折点信息（如果有）
    signals: List[Dict] = field(default_factory=list)  # 生成的交易信号

@dataclass
class Point:
    """价格转折点"""
    point_date: str
    price: float
    point_type: int  # 1=波峰, 2=波谷
    start_date: str
    keep_days: int
    cumulative_volume: float

class EventDrivenTurnPointDetector:
    def __init__(self, min_keep_days=3, min_extent_percent=1.0):
        self.min_keep_days = min_keep_days
        self.min_extent = min_extent_percent / 100.0
        self.event_handlers = []  # 事件处理器列表
        self.reset_state()
    
    def reset_state(self):
        """重置所有状态"""
        self.points = []          # 所有转折点
        self.raw_points = []      # 原始转折点（过滤前）
        self.candidate = None     # 当前候选转折点
        self.last_trend_type = None  # 上一个趋势类型
        self.history = []         # 处理过的历史事件
        
    def register_handler(self, handler: Callable[[MarketEvent], None]):
        """注册事件处理器"""
        self.event_handlers.append(handler)
    
    def process_data(self, df: pd.DataFrame):
        """处理数据并生成事件流"""
        self.reset_state()
        df = self._prepare_data(df.copy())
        
        # 按日期循环处理
        for i in range(1, len(df)):
            current = df.iloc[i]
            previous = df.iloc[i-1]
            
            # 1. 创建市场事件
            event = MarketEvent(
                date=current['date'].strftime('%Y-%m-%d'),
                open=current['open'],
                high=current['high'],
                low=current['low'],
                close=current['close'],
                volume=current['volume']
            )
            
            # 2. 计算技术指标（示例）
            event.indicators = self._calculate_indicators(df, i)
            
            # 3. 处理转折点
            point_event = self._process_turn_point(current, previous, event.date)
            if point_event:
                event.point_info = point_event
            
            # 4. 分发事件给所有处理器
            for handler in self.event_handlers:
                handler(event)
            
            # 5. 保存历史事件（用于后续分析）
            self.history.append(event)
        
        # 处理最后一个候选点
        if self.candidate:
            self._finalize_candidate()
        
        # 过滤转折点
        self._filter_points()
        
        return self.points, self.history

    def _prepare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        # 确保日期格式
        if not pd.api.types.is_datetime64_any_dtype(df['date']):
            df['date'] = pd.to_datetime(df['date'])
        
        # 排序
        df = df.sort_values('date').reset_index(drop=True)
        return df

    def _process_turn_point(self, current, previous, date_str):
        """处理转折点逻辑，返回转折点事件信息（如果有）"""
        # 1. 计算当前状态
        status = 0
        if current['high'] >= previous['high']:
            status += 1
        if current['low'] <= previous['low']:
            status += 2
        
        # 2. 确定当前趋势类型
        if status == 1:
            current_trend_type = 1
        elif status == 2:
            current_trend_type = 2
        else:
            current_trend_type = self.last_trend_type
        
        # 3. 更新或创建候选转折点
        point_event = None
        
        # 趋势类型变化时完成当前候选点
        if self.candidate and self.candidate['point_type'] != current_trend_type:
            point_event = self._finalize_candidate()
        
        # 创建新候选点
        if not self.candidate or self.candidate['point_type'] != current_trend_type:
            self.candidate = {
                'point_type': current_trend_type,
                'price': current['high'] if current_trend_type == 1 else current['low'],
                'start_date': date_str,
                'confirm_date': date_str,
                'keep_days': 1,
                'cumulative_volume': current['volume'],
                'extreme_price': current['high'] if current_trend_type == 1 else current['low']
            }
            self.last_trend_type = current_trend_type
            return point_event
        
        # 更新现有候选点
        self.candidate['keep_days'] += 1
        self.candidate['cumulative_volume'] += current['volume']
        self.candidate['confirm_date'] = date_str
        
        # 更新极值价格
        if current_trend_type == 1:
            self.candidate['extreme_price'] = max(
                self.candidate['extreme_price'], 
                current['high']
            )
        else:
            self.candidate['extreme_price'] = min(
                self.candidate['extreme_price'], 
                current['low']
            )
        
        return point_event

    def _finalize_candidate(self):
        """完成候选点并返回事件信息"""
        if not self.candidate:
            return None
            
        # 创建Point对象
        point = Point(
            point_date=self.candidate['confirm_date'],
            price=self.candidate['extreme_price'],
            point_type=self.candidate['point_type'],
            start_date=self.candidate['start_date'],
            keep_days=self.candidate['keep_days'],
            cumulative_volume=self.candidate['cumulative_volume']
        )
        
        self.raw_points.append(point)
        point_event = {
            'type': 'peak' if point.point_type == 1 else 'trough',
            'price': point.price,
            'date': point.point_date,
            'start_date': point.start_date,
            'duration': point.keep_days,
            'volume': point.cumulative_volume
        }
        
        self.candidate = None
        return point_event

    def _filter_points(self):
        """过滤转折点（简化版）"""
        last_opposite_price = None
        
        for point in self.raw_points:
            # 1. 持续时间过滤
            if point.keep_days < self.min_keep_days:
                continue
                
            # 2. 幅度过滤
            if last_opposite_price:
                extent = (point.price - last_opposite_price) / last_opposite_price
                if abs(extent) < self.min_extent:
                    continue
            
            # 3. 更新相反点价格
            if point.point_type == 1:  # 波峰
                last_opposite_price = point.price
            elif point.point_type == 2:  # 波谷
                last_opposite_price = point.price
                
            self.points.append(point)

    def _calculate_indicators(self, df, index, period=14):
        """计算技术指标（示例实现）"""
        # RSI计算
        if index >= period:
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            rsi_value = rsi.iloc[index]
        else:
            rsi_value = 50  # 默认值
        
        # 移动平均
        ma5 = df['close'].rolling(window=5).mean().iloc[index] if index >= 4 else df['close'].iloc[index]
        ma20 = df['close'].rolling(window=20).mean().iloc[index] if index >= 19 else df['close'].iloc[index]
        
        return {
            'RSI': round(rsi_value, 2),
            'MA5': round(ma5, 2),
            'MA20': round(ma20, 2),
            'Volume': df['volume'].iloc[index]
        }