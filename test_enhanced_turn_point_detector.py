#!/usr/bin/env python3
"""
测试增强转折点检测器

验证量价分析功能：
1. 量价确认：上升下降趋势成交量增加，量价背离信号
2. 识别高成交量价格区域（支撑/阻力区）
3. 转折点成交量验证趋势可靠性
4. 支持阻力位信号
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import List, Dict, Any

from trading_system.analysis.turn_point_detector import TurnPointDetector
from trading_system.core.event import MarketEvent, TurnPointEvent

def generate_test_data(days: int = 100) -> pd.DataFrame:
    """生成测试数据，包含明显的转折点"""
    np.random.seed(42)

    # 生成基础价格走势
    base_price = 100.0
    prices = [base_price]
    volumes = []

    for i in range(days):
        # 生成更明显的价格波动
        if i < 20:
            # 上升趋势
            trend = 0.02
        elif i < 40:
            # 下降趋势
            trend = -0.025
        elif i < 60:
            # 再次上升
            trend = 0.03
        else:
            # 盘整
            trend = 0.005 * np.sin(i * 0.2)

        noise = np.random.normal(0, 0.01)  # 随机噪声
        change = trend + noise

        new_price = prices[-1] * (1 + change)
        prices.append(new_price)

        # 生成成交量（与价格变化和转折点相关）
        price_change = abs(change)
        base_volume = 1000000
        volume_multiplier = 1 + price_change * 15  # 价格变化大时成交量增加

        # 在转折点附近添加异常高成交量
        if i in [18, 19, 20, 21, 22, 38, 39, 40, 41, 42, 58, 59, 60, 61, 62]:  # 转折点附近
            volume_multiplier *= 4
        elif i % 15 == 0:  # 其他高成交量点
            volume_multiplier *= 2

        volume = base_volume * volume_multiplier * (1 + np.random.normal(0, 0.2))
        volumes.append(max(volume, base_volume * 0.1))  # 确保成交量不为负

    # 创建DataFrame
    dates = [datetime.now() - timedelta(days=days-i) for i in range(days+1)]

    df = pd.DataFrame({
        'date': dates,
        'close': prices
    })

    # 生成OHLC数据
    df['open'] = df['close'].shift(1).fillna(df['close'])
    df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.005, len(df)))
    df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.005, len(df)))
    df['volume'] = [1000000] + volumes  # 第一天的成交量

    return df.iloc[1:]  # 去掉第一行（因为open是NaN）

def test_enhanced_detector():
    """测试增强转折点检测器"""
    print("=== 测试增强转折点检测器 ===")
    
    # 生成测试数据
    df = generate_test_data(60)
    print(f"生成了 {len(df)} 天的测试数据")
    
    # 创建检测器（调整参数使转折点更容易被检测）
    detector = TurnPointDetector(
        min_keep_days=1,  # 降低最小持续天数
        min_extent_percent=3.0,  # 提高最小变化幅度要求
        volume_window=10,
        volume_threshold=1.2,  # 降低成交量阈值
        sr_sensitivity=0.02
    )
    
    # 处理数据
    turn_points = []
    market_analyses = []
    
    for i, row in df.iterrows():
        market_event = MarketEvent(
            symbol="TEST",
            timestamp=row['date'],
            open=row['open'],
            high=row['high'],
            low=row['low'],
            close=row['close'],
            volume=row['volume']
        )
        
        # 处理事件
        turn_point = detector.process_market_event(market_event)
        if turn_point:
            turn_points.append(turn_point)
            print(f"\n检测到转折点:")
            print(f"  时间: {turn_point.timestamp}")
            print(f"  类型: {turn_point.point_type}")
            print(f"  价格: {turn_point.price:.2f}")
            print(f"  持续天数: {turn_point.keep_days}")
            print(f"  变化幅度: {turn_point.extent_percent:.2%}")
            print(f"  累计成交量: {turn_point.volume:,.0f}")
        
        # 每10天获取一次市场分析
        if i % 10 == 0:
            analysis = detector.get_market_analysis()
            market_analyses.append((row['date'], analysis))
    
    print(f"\n总共检测到 {len(turn_points)} 个转折点")
    
    # 显示支撑阻力位
    sr_levels = detector.get_support_resistance_levels()
    print(f"\n当前支撑阻力位 (共{len(sr_levels)}个):")
    for i, level in enumerate(sr_levels[:5]):  # 显示前5个最强的
        print(f"  {i+1}. {level.level_type} {level.price:.2f} "
              f"(强度: {level.strength:.2f}, 触及次数: {level.touch_count})")
    
    # 显示当前量价信号
    volume_signals = detector.get_current_volume_signals()
    if volume_signals:
        print(f"\n当前量价信号 (共{len(volume_signals)}个):")
        for signal in volume_signals:
            print(f"  - {signal.signal_type}: {signal.description} (强度: {signal.strength:.2f})")
    
    # 显示最新市场分析
    if market_analyses:
        latest_analysis = market_analyses[-1][1]
        print(f"\n最新市场分析:")
        
        if latest_analysis['volume_analysis']:
            va = latest_analysis['volume_analysis']
            print(f"  成交量分析:")
            print(f"    当前成交量: {va['current_volume']:,.0f}")
            print(f"    平均成交量: {va['avg_volume']:,.0f}")
            print(f"    量比: {va['volume_ratio']:.2f}")
            print(f"    成交量趋势: {va['volume_trend']:.4f}")
        
        if latest_analysis['trend_analysis']:
            ta = latest_analysis['trend_analysis']
            print(f"  趋势分析:")
            print(f"    当前价格: {ta['current_price']:.2f}")
            print(f"    价格趋势: {ta['price_trend']:.4f}")
            print(f"    最后趋势类型: {ta['last_trend_type']}")
    
    return df, turn_points, detector

def plot_results(df: pd.DataFrame, turn_points: List[TurnPointEvent], detector: TurnPointDetector):
    """绘制结果图表"""
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
    
    # 价格图
    ax1.plot(df['date'], df['close'], 'b-', linewidth=1, label='收盘价')
    ax1.plot(df['date'], df['high'], 'g-', alpha=0.3, linewidth=0.5)
    ax1.plot(df['date'], df['low'], 'r-', alpha=0.3, linewidth=0.5)
    
    # 标记转折点
    for tp in turn_points:
        color = 'red' if tp.point_type == 'PEAK' else 'green'
        marker = 'v' if tp.point_type == 'PEAK' else '^'
        ax1.scatter(tp.timestamp, tp.price, color=color, marker=marker, s=100, zorder=5)
        ax1.annotate(f'{tp.point_type}\n{tp.price:.1f}', 
                    (tp.timestamp, tp.price), 
                    xytext=(10, 10), textcoords='offset points',
                    fontsize=8, ha='left')
    
    # 标记支撑阻力位
    sr_levels = detector.get_support_resistance_levels()
    for level in sr_levels[:3]:  # 只显示前3个最强的
        color = 'orange' if level.level_type == 'RESISTANCE' else 'purple'
        ax1.axhline(y=level.price, color=color, linestyle='--', alpha=0.7, linewidth=1)
        ax1.text(df['date'].iloc[-1], level.price, 
                f'{level.level_type[:3]} {level.price:.1f}', 
                fontsize=8, ha='right', va='bottom')
    
    ax1.set_title('价格走势与转折点检测')
    ax1.set_ylabel('价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 成交量图
    ax2.bar(df['date'], df['volume'], alpha=0.7, color='blue', width=0.8)
    
    # 标记高成交量点
    avg_volume = df['volume'].mean()
    high_volume_threshold = avg_volume * 2
    high_volume_dates = df[df['volume'] > high_volume_threshold]['date']
    high_volume_values = df[df['volume'] > high_volume_threshold]['volume']
    ax2.scatter(high_volume_dates, high_volume_values, color='red', s=50, zorder=5)
    
    ax2.axhline(y=avg_volume, color='red', linestyle='--', alpha=0.7, label=f'平均成交量: {avg_volume:,.0f}')
    ax2.axhline(y=high_volume_threshold, color='orange', linestyle='--', alpha=0.7, label=f'高成交量阈值: {high_volume_threshold:,.0f}')
    
    ax2.set_title('成交量分析')
    ax2.set_ylabel('成交量')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 量价比图
    volume_ratio = df['volume'] / avg_volume
    ax3.plot(df['date'], volume_ratio, 'purple', linewidth=1, label='量比')
    ax3.axhline(y=1.0, color='gray', linestyle='-', alpha=0.5, label='基准线')
    ax3.axhline(y=1.5, color='orange', linestyle='--', alpha=0.7, label='放量阈值')
    ax3.fill_between(df['date'], volume_ratio, 1, where=(volume_ratio > 1.5), 
                     color='red', alpha=0.3, label='放量区域')
    
    ax3.set_title('量比分析')
    ax3.set_ylabel('量比')
    ax3.set_xlabel('日期')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 格式化x轴日期
    for ax in [ax1, ax2, ax3]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=5))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    # 运行测试
    df, turn_points, detector = test_enhanced_detector()
    
    # 绘制结果
    print("\n正在生成图表...")
    plot_results(df, turn_points, detector)
    
    print("\n测试完成！")
