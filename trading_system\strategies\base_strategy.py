"""
策略基类

定义交易策略的统一接口和基础功能。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import logging

from ..core.event import MarketEvent, SignalEvent, TurnPointEvent


class BaseStrategy(ABC):
    """
    交易策略基类
    
    定义策略的统一接口，所有具体策略都应继承此类。
    """
    
    def __init__(self, name: str, symbol: str):
        """
        初始化策略
        
        Args:
            name: 策略名称
            symbol: 交易标的
        """
        self.name = name
        self.symbol = symbol
        self.logger = logging.getLogger(__name__)
        
        # 策略状态
        self.enabled = True
        self.position = 0  # 当前持仓（正数为多头，负数为空头）
        self.signals_generated = []
        
        # 策略参数
        self.parameters = {}
        
        # 事件处理器映射
        self.event_handlers = {
            MarketEvent: self.on_market_event,
            TurnPointEvent: self.on_turn_point_event,
        }
    
    @abstractmethod
    def on_market_event(self, event: MarketEvent) -> List[SignalEvent]:
        """
        处理市场数据事件
        
        Args:
            event: 市场数据事件
            
        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
        pass
    
    def on_turn_point_event(self, event: TurnPointEvent) -> List[SignalEvent]:
        """
        处理转折点事件

        Args:
            event: 转折点事件

        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
        # 默认实现，子类可以重写
        self.logger.info(f"on_turn_point_event called: {event.point_type} at {event.price:.2f}")
        return []
    
    def process_event(self, event) -> List[SignalEvent]:
        """
        处理事件的统一入口
        
        Args:
            event: 事件对象
            
        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
        if not self.enabled:
            return []
        
        event_type = type(event)
        if event_type in self.event_handlers:
            try:
                signals = self.event_handlers[event_type](event)
                if signals:
                    self.signals_generated.extend(signals)
                    self.logger.info(f"Strategy {self.name} generated {len(signals)} signals")
                return signals
            except Exception as e:
                self.logger.error(f"Error processing {event_type.__name__} in {self.name}: {e}")
        
        return []
    
    def set_parameter(self, key: str, value: Any):
        """
        设置策略参数
        
        Args:
            key: 参数名
            value: 参数值
        """
        self.parameters[key] = value
        self.logger.info(f"Set parameter {key} = {value} for strategy {self.name}")
    
    def get_parameter(self, key: str, default: Any = None) -> Any:
        """
        获取策略参数
        
        Args:
            key: 参数名
            default: 默认值
            
        Returns:
            Any: 参数值
        """
        return self.parameters.get(key, default)
    
    def enable(self):
        """启用策略"""
        self.enabled = True
        self.logger.info(f"Strategy {self.name} enabled")
    
    def disable(self):
        """禁用策略"""
        self.enabled = False
        self.logger.info(f"Strategy {self.name} disabled")
    
    def reset(self):
        """
        重置策略状态
        """
        self.position = 0
        self.signals_generated.clear()
        self.logger.info(f"Strategy {self.name} reset")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取策略统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'name': self.name,
            'symbol': self.symbol,
            'enabled': self.enabled,
            'position': self.position,
            'signals_count': len(self.signals_generated),
            'parameters': self.parameters.copy()
        }
    
    def _create_signal(self, signal_type: str, quantity: int, 
                      price: Optional[float] = None, reason: str = "",
                      confidence: float = 1.0) -> SignalEvent:
        """
        创建交易信号
        
        Args:
            signal_type: 信号类型 ('BUY', 'SELL', 'HOLD')
            quantity: 交易数量
            price: 期望价格
            reason: 信号原因
            confidence: 信号置信度
            
        Returns:
            SignalEvent: 交易信号事件
        """
        from datetime import datetime
        
        return SignalEvent(
            symbol=self.symbol,
            timestamp=datetime.now(),
            signal_type=signal_type,
            quantity=quantity,
            price=price,
            reason=reason,
            confidence=confidence,
            metadata={
                'strategy': self.name,
                'current_position': self.position
            }
        )
