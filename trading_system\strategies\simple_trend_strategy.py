#!/usr/bin/env python3
"""
简单趋势策略

基于技术指标的实时交易策略，避免转折点检测器的历史确认问题。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from .base_strategy import BaseStrategy
from ..core.event import MarketEvent, SignalEvent


class SimpleTrendStrategy(BaseStrategy):
    """
    简单趋势策略
    
    基于移动平均线和RSI的实时交易策略：
    1. 买入条件：价格突破MA20，RSI < 70
    2. 卖出条件：价格跌破MA20，RSI > 30
    3. 严格的交易冷却期控制
    """
    
    def __init__(self, symbol: str):
        super().__init__("SimpleTrendStrategy", symbol)
        
        # 策略参数
        self.set_parameter('position_size', 100)
        self.set_parameter('stop_loss_percent', 0.05)
        self.set_parameter('take_profit_percent', 0.10)
        self.set_parameter('max_position_hold_days', 15)
        self.set_parameter('min_trade_interval_days', 1)
        self.set_parameter('max_position_size', 1000)
        
        # 策略状态
        self.current_position = 0
        self.entry_price = 0.0
        self.entry_time = None
        self.last_trade_time = None
        self.last_trade_date = None  # 记录最后交易日期
        
        # 信号统计
        self.signal_stats = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'filtered_out': 0,
            'cooldown_filtered': 0
        }
    
    def on_market_event(self, event: MarketEvent) -> List[SignalEvent]:
        """
        处理市场数据事件
        
        Args:
            event: 市场数据事件
            
        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
        signals = []
        
        # 检查止损止盈
        stop_signals = self._check_stop_conditions(event)
        signals.extend(stop_signals)
        
        # 检查持仓时间限制
        time_signals = self._check_time_limits(event)
        signals.extend(time_signals)
        
        # 生成趋势信号
        trend_signals = self._generate_trend_signals(event)
        signals.extend(trend_signals)
        
        return signals
    
    def _generate_trend_signals(self, event: MarketEvent) -> List[SignalEvent]:
        """
        基于趋势生成交易信号
        
        Args:
            event: 市场数据事件
            
        Returns:
            List[SignalEvent]: 交易信号列表
        """
        signals = []
        
        # 检查交易冷却期
        if not self._check_trade_cooldown(event.timestamp):
            self.signal_stats['cooldown_filtered'] += 1
            return signals
        
        # 获取技术指标
        indicators = event.indicators
        if not indicators:
            return signals
        
        current_price = event.close
        ma20 = indicators.get('MA20')
        rsi = indicators.get('RSI')
        
        if ma20 is None or rsi is None:
            return signals
        
        position_size = self.get_parameter('position_size')
        
        # 买入条件：价格突破MA20向上，RSI不超买
        if (self.current_position <= 0 and 
            current_price > ma20 and 
            rsi < 70):
            
            signal = self._create_buy_signal(current_price, event.timestamp, position_size)
            if signal:
                signals.append(signal)
                self.signal_stats['buy_signals'] += 1
        
        # 卖出条件：价格跌破MA20向下，RSI不超卖
        elif (self.current_position >= 0 and 
              current_price < ma20 and 
              rsi > 30):
            
            signal = self._create_sell_signal(current_price, event.timestamp, position_size)
            if signal:
                signals.append(signal)
                self.signal_stats['sell_signals'] += 1
        
        self.signal_stats['total_signals'] += len(signals)
        return signals
    
    def _check_trade_cooldown(self, current_time: datetime) -> bool:
        """
        检查交易冷却期
        
        Args:
            current_time: 当前时间
            
        Returns:
            bool: 是否可以交易
        """
        current_date = current_time.date()
        
        # 如果是同一天，直接拒绝
        if self.last_trade_date == current_date:
            return False
        
        # 检查最小交易间隔
        if self.last_trade_time is None:
            return True
        
        min_interval = self.get_parameter('min_trade_interval_days')
        time_diff = current_time - self.last_trade_time
        
        return time_diff.days >= min_interval
    
    def _create_buy_signal(self, price: float, timestamp: datetime, position_size: int) -> Optional[SignalEvent]:
        """创建买入信号"""
        
        # 检查持仓限制
        if self.current_position >= self.get_parameter('max_position_size'):
            return None
        
        reason = f"趋势买入: 价格突破MA20"
        
        signal = self._create_signal(
            signal_type='BUY',
            quantity=position_size,
            price=None,  # 使用市价单
            reason=reason,
            confidence=0.8
        )
        
        self.logger.info(f"BUY signal generated: {reason} @ {price:.2f} on {timestamp.strftime('%Y-%m-%d')}")
        return signal
    
    def _create_sell_signal(self, price: float, timestamp: datetime, position_size: int) -> Optional[SignalEvent]:
        """创建卖出信号"""
        
        # 确定卖出数量
        if self.current_position > 0:
            sell_quantity = min(self.current_position, position_size)
        else:
            sell_quantity = position_size
        
        reason = f"趋势卖出: 价格跌破MA20"
        
        signal = self._create_signal(
            signal_type='SELL',
            quantity=sell_quantity,
            price=None,  # 使用市价单
            reason=reason,
            confidence=0.8
        )
        
        self.logger.info(f"SELL signal generated: {reason} @ {price:.2f} on {timestamp.strftime('%Y-%m-%d')}")
        return signal
    
    def _check_stop_conditions(self, event: MarketEvent) -> List[SignalEvent]:
        """检查止损止盈条件"""
        signals = []
        
        if self.current_position == 0 or self.entry_price == 0:
            return signals
        
        current_price = event.close
        stop_loss_pct = self.get_parameter('stop_loss_percent')
        take_profit_pct = self.get_parameter('take_profit_percent')
        
        if self.current_position > 0:  # 多头持仓
            # 止损检查
            if current_price <= self.entry_price * (1 - stop_loss_pct):
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=self.current_position,
                    price=None,
                    reason=f'止损 (入场: {self.entry_price:.2f}, 当前: {current_price:.2f})',
                    confidence=1.0
                )
                signals.append(signal)
            
            # 止盈检查
            elif current_price >= self.entry_price * (1 + take_profit_pct):
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=self.current_position,
                    price=None,
                    reason=f'止盈 (入场: {self.entry_price:.2f}, 当前: {current_price:.2f})',
                    confidence=1.0
                )
                signals.append(signal)
        
        return signals
    
    def _check_time_limits(self, event: MarketEvent) -> List[SignalEvent]:
        """检查持仓时间限制"""
        signals = []
        
        if self.current_position == 0 or self.entry_time is None:
            return signals
        
        max_hold_days = self.get_parameter('max_position_hold_days')
        hold_duration = event.timestamp - self.entry_time
        
        if hold_duration.days >= max_hold_days:
            # 超过最大持仓时间，强制平仓
            if self.current_position > 0:
                signal = self._create_signal(
                    signal_type='SELL',
                    quantity=self.current_position,
                    price=None,
                    reason=f'超时平仓 (持仓{hold_duration.days}天)',
                    confidence=0.8
                )
            else:
                signal = self._create_signal(
                    signal_type='BUY',
                    quantity=abs(self.current_position),
                    price=None,
                    reason=f'超时平仓 (持仓{hold_duration.days}天)',
                    confidence=0.8
                )
            
            signals.append(signal)
        
        return signals
    
    def on_fill_event(self, fill_event) -> None:
        """
        处理成交事件，更新策略状态
        
        Args:
            fill_event: 成交事件
        """
        # 更新持仓状态
        if fill_event.direction == 'BUY':
            self.current_position += fill_event.quantity
            self.entry_price = fill_event.fill_price
            self.entry_time = fill_event.timestamp
        else:  # SELL
            self.current_position -= fill_event.quantity
            if self.current_position <= 0:
                self.entry_price = 0.0
                self.entry_time = None
        
        # 更新最后交易时间和日期
        self.last_trade_time = fill_event.timestamp
        self.last_trade_date = fill_event.timestamp.date()
        
        self.logger.info(f"Position updated: {self.current_position} shares, "
                        f"entry_price: {self.entry_price}, last_trade: {self.last_trade_time.strftime('%Y-%m-%d')}")
    
    def reset(self):
        """重置策略状态"""
        super().reset()
        self.current_position = 0
        self.entry_price = 0.0
        self.entry_time = None
        self.last_trade_time = None
        self.last_trade_date = None
        self.signal_stats = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'filtered_out': 0,
            'cooldown_filtered': 0
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        base_stats = super().get_stats()
        
        base_stats.update({
            'current_position': self.current_position,
            'entry_price': self.entry_price,
            'entry_time': self.entry_time,
            'last_trade_time': self.last_trade_time,
            'last_trade_date': str(self.last_trade_date) if self.last_trade_date else None,
            'signal_stats': self.signal_stats.copy()
        })
        
        return base_stats
