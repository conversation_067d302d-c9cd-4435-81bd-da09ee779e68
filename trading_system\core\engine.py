"""
事件驱动引擎

系统的心脏，负责事件的队列管理、分发和主循环控制。
"""

import queue
import threading
import time
from typing import Dict, List, Callable, Any, Optional
from datetime import datetime
import logging

from .event import MarketEvent, SignalEvent, OrderEvent, FillEvent, TurnPointEvent


class EventEngine:
    """
    事件驱动引擎
    
    维护一个事件队列，负责事件的循环处理和分发。
    支持事件监听器的注册和管理。
    """
    
    def __init__(self, max_queue_size: int = 1000):
        """
        初始化事件引擎
        
        Args:
            max_queue_size: 事件队列最大大小
        """
        self.event_queue = queue.Queue(maxsize=max_queue_size)
        self.handlers: Dict[type, List[Callable]] = {}
        self.running = False
        self.thread: Optional[threading.Thread] = None
        self.logger = logging.getLogger(__name__)
        
        # 统计信息
        self.stats = {
            'events_processed': 0,
            'events_dropped': 0,
            'start_time': None,
            'last_event_time': None
        }
    
    def register_handler(self, event_type: type, handler: Callable):
        """
        注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理器函数，接收事件对象作为参数
        """
        if event_type not in self.handlers:
            self.handlers[event_type] = []
        
        self.handlers[event_type].append(handler)
        self.logger.info(f"Registered handler for {event_type.__name__}")
    
    def unregister_handler(self, event_type: type, handler: Callable):
        """
        取消注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理器函数
        """
        if event_type in self.handlers:
            try:
                self.handlers[event_type].remove(handler)
                self.logger.info(f"Unregistered handler for {event_type.__name__}")
            except ValueError:
                self.logger.warning(f"Handler not found for {event_type.__name__}")
    
    def put_event(self, event: Any) -> bool:
        """
        将事件放入队列
        
        Args:
            event: 事件对象
            
        Returns:
            bool: 是否成功放入队列
        """
        try:
            self.event_queue.put_nowait(event)
            return True
        except queue.Full:
            self.stats['events_dropped'] += 1
            self.logger.warning(f"Event queue full, dropped {type(event).__name__}")
            return False
    
    def _process_event(self, event: Any):
        """
        处理单个事件
        
        Args:
            event: 事件对象
        """
        event_type = type(event)
        
        if event_type in self.handlers:
            for handler in self.handlers[event_type]:
                try:
                    handler(event)
                except Exception as e:
                    self.logger.error(f"Error in handler for {event_type.__name__}: {e}")
        
        self.stats['events_processed'] += 1
        self.stats['last_event_time'] = datetime.now()
    
    def _run(self):
        """
        事件循环主函数
        """
        self.logger.info("Event engine started")
        self.stats['start_time'] = datetime.now()
        
        while self.running:
            try:
                # 从队列获取事件，设置超时避免阻塞
                event = self.event_queue.get(timeout=0.1)
                self._process_event(event)
                self.event_queue.task_done()
                
            except queue.Empty:
                # 队列为空，继续循环
                continue
            except Exception as e:
                self.logger.error(f"Error in event loop: {e}")
        
        self.logger.info("Event engine stopped")
    
    def start(self):
        """
        启动事件引擎
        """
        if self.running:
            self.logger.warning("Event engine is already running")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._run, daemon=True)
        self.thread.start()
        self.logger.info("Event engine thread started")
    
    def stop(self):
        """
        停止事件引擎
        """
        if not self.running:
            self.logger.warning("Event engine is not running")
            return
        
        self.running = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5.0)
            if self.thread.is_alive():
                self.logger.warning("Event engine thread did not stop gracefully")
        
        self.logger.info("Event engine stopped")
    
    def wait_for_completion(self, timeout: Optional[float] = None):
        """
        等待所有事件处理完成
        
        Args:
            timeout: 超时时间（秒）
        """
        try:
            if timeout:
                start_time = time.time()
                while not self.event_queue.empty():
                    if time.time() - start_time > timeout:
                        self.logger.warning("Wait for completion timed out")
                        break
                    time.sleep(0.01)
            else:
                self.event_queue.join()
        except KeyboardInterrupt:
            self.logger.info("Wait interrupted by user")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取引擎统计信息
        
        Returns:
            Dict: 统计信息
        """
        stats = self.stats.copy()
        stats['queue_size'] = self.event_queue.qsize()
        stats['handlers_count'] = {
            event_type.__name__: len(handlers) 
            for event_type, handlers in self.handlers.items()
        }
        return stats
    
    def clear_queue(self):
        """
        清空事件队列
        """
        while not self.event_queue.empty():
            try:
                self.event_queue.get_nowait()
                self.event_queue.task_done()
            except queue.Empty:
                break
        
        self.logger.info("Event queue cleared")
