#!/usr/bin/env python3
"""
简单调试 - 检查数据和指标

找出为什么没有交易记录
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trading_system.data import HistoricalDataProvider
from trading_system.analysis import IndicatorCalculator


def main():
    """主函数"""
    print("=" * 80)
    print("简单调试 - 检查数据和指标")
    print("=" * 80)
    
    try:
        # 创建数据提供器
        data_provider = HistoricalDataProvider(
            symbol='003021',
            start_date='20240101',
            end_date='20250630'
        )
        
        indicator_calculator = IndicatorCalculator()
        
        print("检查数据和指标...")
        
        records = []
        for market_event in data_provider.get_data_stream():
            # 计算技术指标
            indicators = indicator_calculator.calculate_all(market_event)
            
            record = {
                'date': market_event.timestamp.strftime('%Y-%m-%d'),
                'price': market_event.close,
                'ma20': indicators.get('MA20'),
                'rsi': indicators.get('RSI'),
                'volume': market_event.volume
            }
            records.append(record)
        
        print(f"总共获取 {len(records)} 条记录")
        
        if len(records) == 0:
            print("❌ 没有获取到任何数据")
            return
        
        # 显示前10条记录
        print(f"\n📊 前10条记录:")
        for i, record in enumerate(records[:10], 1):
            ma20_str = f"{record['ma20']:.2f}" if record['ma20'] is not None else "None"
            rsi_str = f"{record['rsi']:.1f}" if record['rsi'] is not None else "None"
            print(f"  {i:2d}. {record['date']}: Price={record['price']:.2f}, MA20={ma20_str}, RSI={rsi_str}")
        
        # 显示后10条记录
        print(f"\n📊 后10条记录:")
        for i, record in enumerate(records[-10:], len(records)-9):
            ma20_str = f"{record['ma20']:.2f}" if record['ma20'] is not None else "None"
            rsi_str = f"{record['rsi']:.1f}" if record['rsi'] is not None else "None"
            print(f"  {i:2d}. {record['date']}: Price={record['price']:.2f}, MA20={ma20_str}, RSI={rsi_str}")
        
        # 统计有效指标数量
        valid_ma20 = [r for r in records if r['ma20'] is not None]
        valid_rsi = [r for r in records if r['rsi'] is not None]
        
        print(f"\n📈 指标统计:")
        print(f"  有效MA20记录: {len(valid_ma20)} / {len(records)} ({len(valid_ma20)/len(records)*100:.1f}%)")
        print(f"  有效RSI记录: {len(valid_rsi)} / {len(records)} ({len(valid_rsi)/len(records)*100:.1f}%)")
        
        if len(valid_ma20) > 0 and len(valid_rsi) > 0:
            # 找到同时有MA20和RSI的记录
            valid_both = [r for r in records if r['ma20'] is not None and r['rsi'] is not None]
            print(f"  同时有MA20和RSI: {len(valid_both)} / {len(records)} ({len(valid_both)/len(records)*100:.1f}%)")
            
            if len(valid_both) > 0:
                # 检查买入卖出条件
                buy_conditions = []
                sell_conditions = []
                
                for record in valid_both:
                    price = record['price']
                    ma20 = record['ma20']
                    rsi = record['rsi']
                    
                    # 买入条件：价格突破MA20，RSI < 70
                    if price > ma20 and rsi < 70:
                        buy_conditions.append(record)
                    
                    # 卖出条件：价格跌破MA20，RSI > 30
                    if price < ma20 and rsi > 30:
                        sell_conditions.append(record)
                
                print(f"\n🎯 交易条件分析:")
                print(f"  满足买入条件: {len(buy_conditions)} 天")
                print(f"  满足卖出条件: {len(sell_conditions)} 天")
                
                if len(buy_conditions) > 0:
                    print(f"\n🟢 买入条件示例 (前3个):")
                    for i, record in enumerate(buy_conditions[:3], 1):
                        print(f"    {i}. {record['date']}: Price={record['price']:.2f} > MA20={record['ma20']:.2f}, RSI={record['rsi']:.1f} < 70")
                
                if len(sell_conditions) > 0:
                    print(f"\n🔴 卖出条件示例 (前3个):")
                    for i, record in enumerate(sell_conditions[:3], 1):
                        print(f"    {i}. {record['date']}: Price={record['price']:.2f} < MA20={record['ma20']:.2f}, RSI={record['rsi']:.1f} > 30")
                
                # 分析价格趋势
                price_above_ma20 = [r for r in valid_both if r['price'] > r['ma20']]
                price_below_ma20 = [r for r in valid_both if r['price'] < r['ma20']]
                
                print(f"\n📊 价格与MA20关系:")
                print(f"  价格高于MA20: {len(price_above_ma20)} 天 ({len(price_above_ma20)/len(valid_both)*100:.1f}%)")
                print(f"  价格低于MA20: {len(price_below_ma20)} 天 ({len(price_below_ma20)/len(valid_both)*100:.1f}%)")
                
                # RSI分布
                rsi_values = [r['rsi'] for r in valid_both]
                avg_rsi = sum(rsi_values) / len(rsi_values)
                min_rsi = min(rsi_values)
                max_rsi = max(rsi_values)
                
                rsi_oversold = [r for r in rsi_values if r < 30]
                rsi_overbought = [r for r in rsi_values if r > 70]
                
                print(f"\n📊 RSI分布:")
                print(f"  平均RSI: {avg_rsi:.1f}")
                print(f"  RSI范围: {min_rsi:.1f} - {max_rsi:.1f}")
                print(f"  RSI < 30 (超卖): {len(rsi_oversold)} 天")
                print(f"  RSI > 70 (超买): {len(rsi_overbought)} 天")
                
                # 结论
                print(f"\n💡 结论:")
                if len(buy_conditions) == 0 and len(sell_conditions) == 0:
                    print("  ❌ 当前策略条件过于严格，没有满足交易条件的情况")
                    print("  💡 建议调整策略参数:")
                    print("     - 放宽RSI条件 (买入: RSI < 80, 卖出: RSI > 20)")
                    print("     - 或使用更短期的移动平均线 (MA5 或 MA10)")
                elif len(buy_conditions) > 0 or len(sell_conditions) > 0:
                    print("  ✅ 有满足交易条件的情况，策略应该能生成信号")
                    print("  🔍 如果没有交易记录，可能是冷却期机制或其他逻辑问题")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 系统运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
