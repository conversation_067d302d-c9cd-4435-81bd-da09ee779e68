#!/usr/bin/env python3
"""
测试简单趋势策略

验证交易是否分布在不同日期
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
from typing import Dict, Any

from trading_system.core import EventEngine, MarketEvent, SignalEvent, OrderEvent, FillEvent
from trading_system.data import HistoricalDataProvider
from trading_system.analysis import IndicatorCalculator
from trading_system.strategies import SimpleTrendStrategy
from trading_system.execution import BacktestExecutionHandler
from trading_system.portfolio import PortfolioManager
# from trading_system.reporting import PerformanceReporter


class SimpleTradingSystem:
    """简单交易系统"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = self._setup_logging()
        
        # 核心组件
        self.event_engine = EventEngine()
        self.data_provider = HistoricalDataProvider(
            symbol=config['symbol'],
            start_date=config['start_date'],
            end_date=config['end_date']
        )
        self.indicator_calculator = IndicatorCalculator()
        self.strategy = SimpleTrendStrategy(config['symbol'])
        self.execution_handler = BacktestExecutionHandler(
            event_engine=self.event_engine,
            slippage_pct=config.get('slippage_pct', 0.001)
        )
        self.portfolio_manager = PortfolioManager(
            initial_cash=config['initial_cash']
        )
        # self.performance_reporter = PerformanceReporter()
        
        # 设置策略参数
        for param, value in config.items():
            if param.startswith(('position_size', 'stop_loss', 'take_profit', 'max_position', 'min_trade')):
                self.strategy.set_parameter(param, value)
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def initialize(self):
        """初始化系统"""
        self.logger.info("Initializing simple trading system...")
        
        # 注册事件处理器
        self.event_engine.register_handler(MarketEvent, self._on_market_event)
        self.event_engine.register_handler(SignalEvent, self._on_signal_event)
        self.event_engine.register_handler(OrderEvent, self._on_order_event)
        self.event_engine.register_handler(FillEvent, self._on_fill_event)
        
        self.logger.info("System initialized successfully")
    
    def _on_market_event(self, event: MarketEvent):
        """处理市场数据事件"""
        # 计算技术指标
        indicators = self.indicator_calculator.calculate_all(event)
        event.indicators.update(indicators)
        
        # 策略处理
        signals = self.strategy.on_market_event(event)
        for signal in signals:
            self.event_engine.put_event(signal)
        
        # 更新执行处理器的市场数据
        self.execution_handler.update_market_data(event)
        
        # 更新投资组合市值
        self.portfolio_manager.update_market_value(event)
    
    def _on_signal_event(self, event: SignalEvent):
        """处理交易信号事件"""
        orders = self.execution_handler.process_signal(event)
        for order in orders:
            self.event_engine.put_event(order)
    
    def _on_order_event(self, event: OrderEvent):
        """处理订单事件"""
        fill_event = self.execution_handler.execute_order(event)
        if fill_event:
            self.event_engine.put_event(fill_event)
    
    def _on_fill_event(self, event: FillEvent):
        """处理成交事件"""
        # 更新策略状态
        self.strategy.on_fill_event(event)
        
        # 更新投资组合
        self.portfolio_manager.process_fill_event(event)
    
    def run_backtest(self):
        """运行回测"""
        self.logger.info("Starting backtest...")
        
        # 启动事件引擎
        self.event_engine.start()
        
        try:
            event_count = 0
            for market_event in self.data_provider.get_data_stream():
                self.event_engine.put_event(market_event)
                event_count += 1
                
                if event_count % 100 == 0:
                    self.logger.info(f"已处理 {event_count} 条 market events")
            
            # 等待所有事件处理完成
            self.event_engine.wait_for_completion(timeout=10)
            
            self.logger.info(f"回测结束. 处理完成 {event_count} 条 market events")
            
        finally:
            self.event_engine.stop()
    
    def show_results(self, save_charts: bool = True):
        """显示结果"""
        # 获取交易记录
        trades = self.portfolio_manager.get_trade_history()
        portfolio_value = self.portfolio_manager.get_portfolio_value()

        # 简化报告生成
        report = self._generate_simple_report(trades, portfolio_value)
        
        # 显示策略统计
        strategy_stats = self.strategy.get_stats()
        print("\n" + "=" * 60)
        print("简单趋势策略分析报告")
        print("=" * 60)
        print(f"策略名称: {strategy_stats['name']}")
        print(f"交易标的: {self.config['symbol']}")
        print(f"当前持仓: {strategy_stats['current_position']}")
        print(f"最后交易日期: {strategy_stats['last_trade_date']}")
        
        signal_stats = strategy_stats['signal_stats']
        print(f"\n信号统计:")
        print(f"  总信号数: {signal_stats['total_signals']}")
        print(f"  买入信号: {signal_stats['buy_signals']}")
        print(f"  卖出信号: {signal_stats['sell_signals']}")
        print(f"  冷却期过滤: {signal_stats['cooldown_filtered']}")
        print("=" * 60)
        
        # 显示详细报告
        print(report)
        
        # 简化图表保存
        if save_charts:
            print(f"\n📊 交易记录已保存 (共{len(trades)}笔交易)")

    def _generate_simple_report(self, trades, portfolio_value):
        """生成简化报告"""
        if not trades:
            return "无交易记录"

        # 统计交易日期分布
        trade_dates = set()
        for trade in trades:
            trade_dates.add(trade['timestamp'].strftime('%Y-%m-%d'))

        # 计算基本统计
        total_trades = len(trades)
        initial_cash = self.config['initial_cash']
        final_value = portfolio_value
        total_return = (final_value - initial_cash) / initial_cash * 100

        report = f"""
================================================================================
                       简单趋势策略回测报告
================================================================================

📊 Portfolio Overview:
  Initial Capital:     ¥  {initial_cash:,.2f}
  Final Value:         ¥  {final_value:,.2f}
  Total Return:               {total_return:.2f}%

💼 Trading Statistics:
  Total Trades:                  {total_trades}
  Trading Days:                  {len(trade_dates)}

📅 Trading Date Distribution:
"""

        # 显示交易日期分布
        sorted_dates = sorted(trade_dates)
        for i, date in enumerate(sorted_dates, 1):
            date_trades = [t for t in trades if t['timestamp'].strftime('%Y-%m-%d') == date]
            report += f"  {i:2d}. {date}: {len(date_trades)} 笔交易\n"

        report += "\n" + "=" * 80
        return report


def create_simple_config() -> Dict[str, Any]:
    """创建简单策略配置"""
    return {
        # 基本配置
        'symbol': '003021',
        'start_date': '20200101',
        'end_date': '20250630',
        'initial_cash': 100000,
        'slippage_pct': 0.001,
        
        # 简单策略配置
        'position_size': 100,
        'stop_loss_percent': 0.05,
        'take_profit_percent': 0.10,
        'max_position_hold_days': 15,
        'min_trade_interval_days': 1,
        'max_position_size': 1000
    }


def main():
    """主函数"""
    print("=" * 100)
    print("简单趋势策略测试")
    print("验证交易是否分布在不同日期")
    print("=" * 100)

        # 创建配置
        config = create_simple_config()

        # 显示配置信息
        print(f"交易标的: {config['symbol']}")
        print(f"回测期间: {config['start_date']} - {config['end_date']}")
        print(f"初始资金: ¥{config['initial_cash']:,}")
        print(f"策略类型: 简单趋势策略 (基于MA20和RSI)")
        print("-" * 100)

        # 创建交易系统
        trading_system = SimpleTradingSystem(config)

        # 初始化系统
        trading_system.initialize()

        # 运行回测
        trading_system.run_backtest()

        # 显示结果
        trading_system.show_results(save_charts=True)

    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n系统运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
