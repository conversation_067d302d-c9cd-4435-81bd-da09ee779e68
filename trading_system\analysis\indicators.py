"""
技术指标计算器

提供状态化的技术指标计算，适配流式数据处理。
每个指标维护自己的计算窗口和状态。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from collections import deque
import numpy as np
import logging

from ..core.event import MarketEvent


class BaseIndicator(ABC):
    """
    技术指标基类
    
    定义指标计算的统一接口。
    """
    
    def __init__(self, name: str, period: int = 14):
        """
        初始化指标
        
        Args:
            name: 指标名称
            period: 计算周期
        """
        self.name = name
        self.period = period
        self.data_window = deque(maxlen=period * 2)  # 保留更多数据用于计算
        self.logger = logging.getLogger(__name__)
    
    @abstractmethod
    def calculate(self, market_event: MarketEvent) -> Optional[float]:
        """
        计算指标值
        
        Args:
            market_event: 市场数据事件
            
        Returns:
            Optional[float]: 指标值，如果数据不足返回None
        """
        pass
    
    def reset(self):
        """
        重置指标状态
        """
        self.data_window.clear()


class RSIIndicator(BaseIndicator):
    """
    相对强弱指数(RSI)指标
    
    计算价格变动的相对强度，用于判断超买超卖。
    """
    
    def __init__(self, period: int = 14):
        super().__init__("RSI", period)
        self.gains = deque(maxlen=period)
        self.losses = deque(maxlen=period)
        self.last_close = None
    
    def calculate(self, market_event: MarketEvent) -> Optional[float]:
        """
        计算RSI值
        
        Args:
            market_event: 市场数据事件
            
        Returns:
            Optional[float]: RSI值 (0-100)
        """
        current_close = market_event.close
        
        if self.last_close is not None:
            # 计算价格变化
            change = current_close - self.last_close
            
            if change > 0:
                self.gains.append(change)
                self.losses.append(0)
            else:
                self.gains.append(0)
                self.losses.append(abs(change))
            
            # 如果数据足够，计算RSI
            if len(self.gains) >= self.period:
                avg_gain = sum(self.gains) / len(self.gains)
                avg_loss = sum(self.losses) / len(self.losses)
                
                if avg_loss == 0:
                    return 100.0
                
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                
                self.last_close = current_close
                return round(rsi, 2)
        
        self.last_close = current_close
        return None


class MovingAverageIndicator(BaseIndicator):
    """
    移动平均线指标
    
    计算指定周期的简单移动平均。
    """
    
    def __init__(self, period: int = 20, ma_type: str = "SMA"):
        super().__init__(f"MA{period}", period)
        self.ma_type = ma_type
        self.prices = deque(maxlen=period)
    
    def calculate(self, market_event: MarketEvent) -> Optional[float]:
        """
        计算移动平均值
        
        Args:
            market_event: 市场数据事件
            
        Returns:
            Optional[float]: 移动平均值
        """
        self.prices.append(market_event.close)
        
        if len(self.prices) >= self.period:
            if self.ma_type == "SMA":
                # 简单移动平均
                return round(sum(self.prices) / len(self.prices), 2)
            elif self.ma_type == "EMA":
                # 指数移动平均（简化实现）
                alpha = 2 / (self.period + 1)
                ema = self.prices[0]
                for price in list(self.prices)[1:]:
                    ema = alpha * price + (1 - alpha) * ema
                return round(ema, 2)
        
        return None


class VolumeIndicator(BaseIndicator):
    """
    成交量指标
    
    计算成交量相关指标。
    """
    
    def __init__(self, period: int = 20):
        super().__init__("Volume", period)
        self.volumes = deque(maxlen=period)
    
    def calculate(self, market_event: MarketEvent) -> Optional[float]:
        """
        计算平均成交量
        
        Args:
            market_event: 市场数据事件
            
        Returns:
            Optional[float]: 平均成交量
        """
        self.volumes.append(market_event.volume)
        
        if len(self.volumes) >= self.period:
            return round(sum(self.volumes) / len(self.volumes), 0)
        
        return None


class IndicatorCalculator:
    """
    指标计算器管理器
    
    管理多个技术指标的计算和状态。
    """
    
    def __init__(self):
        self.indicators: Dict[str, BaseIndicator] = {}
        self.logger = logging.getLogger(__name__)

        # 自动创建默认指标
        self.create_default_indicators()
    
    def add_indicator(self, indicator: BaseIndicator):
        """
        添加指标
        
        Args:
            indicator: 指标实例
        """
        self.indicators[indicator.name] = indicator
        self.logger.info(f"Added indicator: {indicator.name}")
    
    def remove_indicator(self, name: str):
        """
        移除指标
        
        Args:
            name: 指标名称
        """
        if name in self.indicators:
            del self.indicators[name]
            self.logger.info(f"Removed indicator: {name}")
    
    def calculate_all(self, market_event: MarketEvent) -> Dict[str, float]:
        """
        计算所有指标
        
        Args:
            market_event: 市场数据事件
            
        Returns:
            Dict[str, float]: 指标名称到值的映射
        """
        results = {}
        
        for name, indicator in self.indicators.items():
            try:
                value = indicator.calculate(market_event)
                if value is not None:
                    results[name] = value
            except Exception as e:
                self.logger.error(f"Error calculating {name}: {e}")
        
        return results
    
    def reset_all(self):
        """
        重置所有指标状态
        """
        for indicator in self.indicators.values():
            indicator.reset()
        self.logger.info("Reset all indicators")
    
    def get_indicator_names(self) -> List[str]:
        """
        获取所有指标名称
        
        Returns:
            List[str]: 指标名称列表
        """
        return list(self.indicators.keys())
    
    def create_default_indicators(self):
        """
        创建默认指标集合
        """
        # RSI指标
        self.add_indicator(RSIIndicator(14))
        
        # 移动平均线
        self.add_indicator(MovingAverageIndicator(5, "SMA"))
        self.add_indicator(MovingAverageIndicator(20, "SMA"))
        self.add_indicator(MovingAverageIndicator(60, "SMA"))
        
        # 成交量指标
        self.add_indicator(VolumeIndicator(20))
        
        self.logger.info("Created default indicators")
