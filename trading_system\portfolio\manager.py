"""
投资组合管理器

负责管理资金、持仓和风险控制。
根据成交回报更新账户状态，计算收益和风险指标。
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field
import logging

from ..core.event import FillEvent, MarketEvent


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: int = 0                    # 持仓数量（正数为多头，负数为空头）
    avg_cost: float = 0.0               # 平均成本
    market_value: float = 0.0           # 市值
    unrealized_pnl: float = 0.0         # 未实现盈亏
    realized_pnl: float = 0.0           # 已实现盈亏
    last_price: float = 0.0             # 最新价格


@dataclass
class PortfolioSnapshot:
    """投资组合快照"""
    timestamp: datetime
    cash: float
    total_value: float
    positions: Dict[str, Position] = field(default_factory=dict)
    daily_pnl: float = 0.0
    total_pnl: float = 0.0


class PortfolioManager:
    """
    投资组合管理器
    
    管理现金、持仓和风险，提供投资组合的完整状态跟踪。
    """
    
    def __init__(self, initial_cash: float = 100000.0):
        """
        初始化投资组合管理器
        
        Args:
            initial_cash: 初始现金
        """
        self.initial_cash = initial_cash
        self.cash = initial_cash
        self.positions: Dict[str, Position] = {}
        
        # 历史记录
        self.portfolio_history: List[PortfolioSnapshot] = []
        self.trade_history: List[Dict[str, Any]] = []
        
        # 统计信息
        self.total_commission = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        
        self.logger = logging.getLogger(__name__)
        
        # 创建初始快照
        self._create_snapshot(datetime.now())
    
    def process_fill_event(self, fill_event: FillEvent):
        """
        处理成交事件，更新持仓和现金
        
        Args:
            fill_event: 成交事件
        """
        symbol = fill_event.symbol
        quantity = fill_event.quantity
        price = fill_event.fill_price
        commission = fill_event.commission
        direction = fill_event.direction
        
        # 确保持仓记录存在
        if symbol not in self.positions:
            self.positions[symbol] = Position(symbol=symbol)
        
        position = self.positions[symbol]
        
        # 计算交易金额
        trade_value = quantity * price
        
        if direction == 'BUY':
            # 买入操作
            self._process_buy(position, quantity, price, commission)
            self.cash -= (trade_value + commission)
        else:  # SELL
            # 卖出操作
            realized_pnl = self._process_sell(position, quantity, price, commission)
            self.cash += (trade_value - commission)
        
        # 更新统计
        self.total_commission += commission
        self.total_trades += 1
        
        # 记录交易历史
        trade_record = {
            'timestamp': fill_event.timestamp,
            'symbol': symbol,
            'direction': direction,
            'quantity': quantity,
            'price': price,
            'commission': commission,
            'cash_after': self.cash,
            'fill_id': fill_event.fill_id
        }
        self.trade_history.append(trade_record)
        
        self.logger.info(f"Processed fill: {direction} {quantity} {symbol} @ {price:.2f}, "
                        f"Cash: {self.cash:.2f}")
    
    def _process_buy(self, position: Position, quantity: int, price: float, commission: float):
        """
        处理买入操作
        
        Args:
            position: 持仓对象
            quantity: 买入数量
            price: 买入价格
            commission: 手续费
        """
        if position.quantity >= 0:
            # 增加多头持仓
            total_cost = position.quantity * position.avg_cost + quantity * price + commission
            position.quantity += quantity
            position.avg_cost = total_cost / position.quantity if position.quantity > 0 else 0
        else:
            # 减少空头持仓（平空）
            if quantity <= abs(position.quantity):
                # 部分或完全平空
                realized_pnl = quantity * (position.avg_cost - price) - commission
                position.realized_pnl += realized_pnl
                position.quantity += quantity
                
                if position.quantity == 0:
                    position.avg_cost = 0
            else:
                # 平空后转多头
                cover_quantity = abs(position.quantity)
                long_quantity = quantity - cover_quantity
                
                # 平空部分
                realized_pnl = cover_quantity * (position.avg_cost - price) - commission/2
                position.realized_pnl += realized_pnl
                
                # 多头部分
                position.quantity = long_quantity
                position.avg_cost = price + commission/(2*long_quantity) if long_quantity > 0 else 0
    
    def _process_sell(self, position: Position, quantity: int, price: float, commission: float) -> float:
        """
        处理卖出操作
        
        Args:
            position: 持仓对象
            quantity: 卖出数量
            price: 卖出价格
            commission: 手续费
            
        Returns:
            float: 已实现盈亏
        """
        realized_pnl = 0.0
        
        if position.quantity > 0:
            # 减少多头持仓（平多）
            if quantity <= position.quantity:
                # 部分或完全平多
                realized_pnl = quantity * (price - position.avg_cost) - commission
                position.realized_pnl += realized_pnl
                position.quantity -= quantity
                
                if position.quantity == 0:
                    position.avg_cost = 0
                    
                # 统计盈利交易
                if realized_pnl > 0:
                    self.winning_trades += 1
            else:
                # 平多后转空头
                close_quantity = position.quantity
                short_quantity = quantity - close_quantity
                
                # 平多部分
                realized_pnl = close_quantity * (price - position.avg_cost) - commission/2
                position.realized_pnl += realized_pnl
                
                if realized_pnl > 0:
                    self.winning_trades += 1
                
                # 空头部分
                position.quantity = -short_quantity
                position.avg_cost = price + commission/(2*short_quantity) if short_quantity > 0 else 0
        else:
            # 增加空头持仓
            if position.quantity == 0:
                # 开空
                position.quantity = -quantity
                position.avg_cost = price + commission/quantity
            else:
                # 增加空头
                total_cost = abs(position.quantity) * position.avg_cost + quantity * price + commission
                position.quantity -= quantity
                position.avg_cost = total_cost / abs(position.quantity) if position.quantity != 0 else 0
        
        return realized_pnl
    
    def update_market_value(self, market_event: MarketEvent):
        """
        根据市场数据更新持仓市值
        
        Args:
            market_event: 市场数据事件
        """
        symbol = market_event.symbol
        current_price = market_event.close
        
        if symbol in self.positions:
            position = self.positions[symbol]
            position.last_price = current_price
            
            if position.quantity != 0:
                # 更新市值
                position.market_value = abs(position.quantity) * current_price
                
                # 计算未实现盈亏
                if position.quantity > 0:
                    # 多头
                    position.unrealized_pnl = position.quantity * (current_price - position.avg_cost)
                else:
                    # 空头
                    position.unrealized_pnl = abs(position.quantity) * (position.avg_cost - current_price)
            else:
                position.market_value = 0.0
                position.unrealized_pnl = 0.0
        
        # 创建投资组合快照
        self._create_snapshot(market_event.timestamp)
    
    def _create_snapshot(self, timestamp: datetime):
        """
        创建投资组合快照
        
        Args:
            timestamp: 时间戳
        """
        # 计算总市值
        total_market_value = sum(pos.market_value for pos in self.positions.values())
        total_value = self.cash + total_market_value
        
        # 计算总盈亏
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        total_realized_pnl = sum(pos.realized_pnl for pos in self.positions.values())
        total_pnl = total_realized_pnl + total_unrealized_pnl
        
        # 计算日盈亏
        daily_pnl = 0.0
        if len(self.portfolio_history) > 0:
            last_snapshot = self.portfolio_history[-1]
            daily_pnl = total_value - last_snapshot.total_value
        
        snapshot = PortfolioSnapshot(
            timestamp=timestamp,
            cash=self.cash,
            total_value=total_value,
            positions=self.positions.copy(),
            daily_pnl=daily_pnl,
            total_pnl=total_pnl
        )
        
        self.portfolio_history.append(snapshot)
    
    def get_current_value(self) -> float:
        """
        获取当前投资组合总价值
        
        Returns:
            float: 总价值
        """
        total_market_value = sum(pos.market_value for pos in self.positions.values())
        return self.cash + total_market_value
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """
        获取指定标的的持仓
        
        Args:
            symbol: 标的代码
            
        Returns:
            Optional[Position]: 持仓信息
        """
        return self.positions.get(symbol)
    
    def get_all_positions(self) -> Dict[str, Position]:
        """
        获取所有持仓
        
        Returns:
            Dict[str, Position]: 所有持仓
        """
        return self.positions.copy()
    
    def calculate_max_drawdown(self) -> float:
        """
        计算最大回撤
        
        Returns:
            float: 最大回撤比例
        """
        if len(self.portfolio_history) < 2:
            return 0.0
        
        values = [snapshot.total_value for snapshot in self.portfolio_history]
        peak = values[0]
        max_drawdown = 0.0
        
        for value in values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        return max_drawdown
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取绩效统计
        
        Returns:
            Dict[str, Any]: 绩效统计信息
        """
        current_value = self.get_current_value()
        total_return = (current_value - self.initial_cash) / self.initial_cash
        
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        
        return {
            'initial_cash': self.initial_cash,
            'current_value': current_value,
            'total_return': total_return,
            'total_return_pct': total_return * 100,
            'max_drawdown': self.calculate_max_drawdown(),
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': win_rate,
            'total_commission': self.total_commission,
            'cash': self.cash,
            'positions_count': len([p for p in self.positions.values() if p.quantity != 0])
        }

    def get_trade_history(self) -> List[Dict[str, Any]]:
        """
        获取交易历史记录

        Returns:
            List[Dict[str, Any]]: 交易历史记录列表
        """
        return self.trade_history.copy()

    def get_detailed_trade_summary(self) -> Dict[str, Any]:
        """
        获取详细的交易汇总信息 (修复版)
        修复了买卖匹配逻辑，正确处理部分成交和FIFO原则。
        """
        if not self.trade_history:
            return {
                'total_trades': 0, 'buy_trades': 0, 'sell_trades': 0,
                'total_volume': 0, 'total_commission': 0, 'completed_trades': 0,
                'profitable_trades': [], 'losing_trades': [], 'break_even_trades': [],
                'avg_profit': 0, 'avg_loss': 0, 'avg_hold_days': 0,
                'best_trade': None, 'worst_trade': None
            }
    
        sorted_trades = sorted(self.trade_history, key=lambda x: x['timestamp'])
        buy_trades_all = [t for t in sorted_trades if t['direction'] == 'BUY']
        sell_trades_all = [t for t in sorted_trades if t['direction'] == 'SELL']
    
        # 使用FIFO原则匹配买卖交易
        completed_trades = []
        open_buys = [t.copy() for t in buy_trades_all]
    
        for sell_trade in sell_trades_all:
            sell_qty_rem = sell_trade['quantity']
            
            while sell_qty_rem > 0 and open_buys:
                buy_trade = open_buys[0]
                
                match_qty = min(sell_qty_rem, buy_trade['quantity'])
                
                if match_qty <= 0:
                    open_buys.pop(0)
                    continue
    
                # 计算盈亏
                profit = (sell_trade['price'] - buy_trade['price']) * match_qty
                
                # 按比例分配手续费
                buy_commission_per_share = buy_trade['commission'] / buy_trade['quantity'] if buy_trade['quantity'] != 0 else 0
                sell_commission_per_share = sell_trade['commission'] / sell_trade['quantity'] if sell_trade['quantity'] != 0 else 0
                total_commission_for_match = (buy_commission_per_share + sell_commission_per_share) * match_qty
                net_profit = profit - total_commission_for_match
    
                completed_trade = {
                    'buy_date': buy_trade['timestamp'],
                    'sell_date': sell_trade['timestamp'],
                    'buy_price': buy_trade['price'],
                    'sell_price': sell_trade['price'],
                    'quantity': match_qty,
                    'profit': net_profit,
                    'return_pct': (sell_trade['price'] - buy_trade['price']) / buy_trade['price'] * 100 if buy_trade['price'] != 0 else 0,
                    'hold_days': (sell_trade['timestamp'] - buy_trade['timestamp']).days
                }
                completed_trades.append(completed_trade)
    
                # 更新剩余数量
                sell_qty_rem -= match_qty
                buy_trade['quantity'] -= match_qty
    
                if buy_trade['quantity'] < 1e-6: # 浮点数比较
                    open_buys.pop(0)
    
        # 分类交易
        profitable_trades = [t for t in completed_trades if t['profit'] > 0]
        losing_trades = [t for t in completed_trades if t['profit'] < 0]
        break_even_trades = [t for t in completed_trades if abs(t['profit']) < 1e-6]
    
        total_volume = sum(t['quantity'] * t['price'] for t in self.trade_history)
    
        return {
            'total_trades': len(self.trade_history),
            'buy_trades': len(buy_trades_all),
            'sell_trades': len(sell_trades_all),
            'completed_trades': len(completed_trades),
            'total_volume': total_volume,
            'total_commission': self.total_commission,
            'profitable_trades': profitable_trades,
            'losing_trades': losing_trades,
            'break_even_trades': break_even_trades,
            'avg_profit': sum(t['profit'] for t in profitable_trades) / len(profitable_trades) if profitable_trades else 0,
            'avg_loss': sum(t['profit'] for t in losing_trades) / len(losing_trades) if losing_trades else 0,
            'avg_hold_days': sum(t['hold_days'] for t in completed_trades) / len(completed_trades) if completed_trades else 0,
            'best_trade': max(completed_trades, key=lambda x: x['profit']) if completed_trades else None,
            'worst_trade': min(completed_trades, key=lambda x: x['profit']) if completed_trades else None
        }
    
    def reset(self):
        """重置投资组合状态"""
        self.cash = self.initial_cash
        self.positions.clear()
        self.portfolio_history.clear()
        self.trade_history.clear()
        self.total_commission = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        
        # 创建初始快照
        self._create_snapshot(datetime.now())
        
        self.logger.info("Portfolio manager reset")
