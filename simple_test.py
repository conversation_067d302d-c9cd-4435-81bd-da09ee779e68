#!/usr/bin/env python3
"""
简单测试增强转折点检测器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from trading_system.analysis.turn_point_detector import TurnPointDetector
from trading_system.core.event import MarketEvent

def test_simple():
    print("=== 简单测试增强转折点检测器 ===")
    
    # 创建检测器
    detector = TurnPointDetector(
        min_keep_days=1,
        min_extent_percent=5.0,  # 5%的变化幅度
        volume_window=5,
        volume_threshold=1.2,
        sr_sensitivity=0.02
    )
    
    # 创建测试数据 - 明显的上升然后下降
    test_data = [
        (100.0, 1000000),  # 起始点
        (102.0, 1200000),  # 上升
        (105.0, 1500000),  # 继续上升
        (108.0, 2000000),  # 高成交量上升
        (110.0, 1800000),  # 峰值
        (109.0, 1600000),  # 开始下降
        (106.0, 1400000),  # 继续下降
        (103.0, 1800000),  # 下降
        (100.0, 2200000),  # 谷底
        (102.0, 1500000),  # 反弹
    ]
    
    turn_points = []
    base_date = datetime.now() - timedelta(days=len(test_data))
    
    for i, (price, volume) in enumerate(test_data):
        market_event = MarketEvent(
            symbol="TEST",
            timestamp=base_date + timedelta(days=i),
            open=price * 0.99,
            high=price * 1.01,
            low=price * 0.98,
            close=price,
            volume=volume
        )
        
        print(f"Day {i+1}: Price={price:.2f}, Volume={volume:,}")
        
        # 处理事件
        turn_point = detector.process_market_event(market_event)
        if turn_point:
            turn_points.append(turn_point)
            print(f"  *** 检测到转折点: {turn_point.point_type} at {turn_point.price:.2f} ***")
        
        # 显示当前状态
        candidate = detector.get_current_candidate()
        if candidate:
            print(f"  候选点: {candidate.point_type} at {candidate.price:.2f} (持续{candidate.keep_days}天)")
        
        # 显示量价信号
        volume_signals = detector.get_current_volume_signals()
        if volume_signals:
            print(f"  量价信号: {len(volume_signals)}个")
            for signal in volume_signals[-2:]:  # 显示最新的2个信号
                print(f"    - {signal.signal_type}: {signal.description[:50]}...")
        
        print()
    
    print(f"总共检测到 {len(turn_points)} 个转折点")
    
    # 显示支撑阻力位
    sr_levels = detector.get_support_resistance_levels()
    print(f"\n支撑阻力位 (共{len(sr_levels)}个):")
    for level in sr_levels:
        print(f"  {level.level_type} {level.price:.2f} (强度: {level.strength:.2f})")
    
    # 显示市场分析
    analysis = detector.get_market_analysis()
    print(f"\n市场分析:")
    if analysis['volume_analysis']:
        va = analysis['volume_analysis']
        print(f"  当前量比: {va['volume_ratio']:.2f}")
        print(f"  成交量趋势: {va['volume_trend']:.4f}")
    
    return turn_points, detector

if __name__ == "__main__":
    turn_points, detector = test_simple()
    print("\n测试完成！")
