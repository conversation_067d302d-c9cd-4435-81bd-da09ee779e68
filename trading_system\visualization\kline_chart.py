"""
K线图可视化模块

生成带转折点标志的K线图，包含成交量、阻力位标注和量价背离信息
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import logging

from ..core.event import MarketEvent, TurnPointEvent
from ..analysis.turn_point_detector import TurnPointDetector, SupportResistanceLevel, VolumeSignal


class KLineChartVisualizer:
    """K线图可视化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
    def create_kline_chart(self, 
                          market_data: List[MarketEvent],
                          turn_points: List[TurnPointEvent],
                          detector: TurnPointDetector,
                          symbol: str,
                          save_path: Optional[str] = None,
                          figsize: Tuple[int, int] = (16, 12)) -> None:
        """
        创建带转折点标志的K线图
        
        Args:
            market_data: 市场数据列表
            turn_points: 转折点列表
            detector: 转折点检测器（用于获取支撑阻力位等信息）
            symbol: 股票代码
            save_path: 保存路径，如果为None则显示图表
            figsize: 图表大小
        """
        if not market_data:
            self.logger.warning("No market data provided")
            return
            
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, 
                                      gridspec_kw={'height_ratios': [3, 1]},
                                      sharex=True)
        
        # 转换数据为DataFrame
        df = self._convert_to_dataframe(market_data)
        
        # 绘制K线图
        self._plot_candlesticks(ax1, df)
        
        # 绘制转折点标志
        self._plot_turn_points(ax1, turn_points, df)
        
        # 绘制支撑阻力位
        self._plot_support_resistance(ax1, detector.get_support_resistance_levels(), df)
        
        # 绘制量价背离信号
        self._plot_volume_divergence(ax1, detector, df)
        
        # 绘制成交量
        self._plot_volume(ax2, df)
        
        # 设置图表样式
        self._setup_chart_style(ax1, ax2, symbol, df)
        
        # 添加图例和标注
        self._add_legends_and_annotations(ax1, ax2, detector)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"Chart saved to {save_path}")
        else:
            plt.show()
            
    def _convert_to_dataframe(self, market_data: List[MarketEvent]) -> pd.DataFrame:
        """将市场数据转换为DataFrame"""
        data = []
        for event in market_data:
            data.append({
                'timestamp': event.timestamp,
                'open': event.open,
                'high': event.high,
                'low': event.low,
                'close': event.close,
                'volume': event.volume
            })
        
        df = pd.DataFrame(data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')
        return df
        
    def _plot_candlesticks(self, ax, df: pd.DataFrame):
        """绘制K线图"""
        # 计算涨跌
        up = df['close'] >= df['open']
        down = ~up
        
        # 绘制实体
        ax.bar(df.index[up], df['close'][up] - df['open'][up], 
               bottom=df['open'][up], color='red', alpha=0.8, width=0.8)
        ax.bar(df.index[down], df['open'][down] - df['close'][down], 
               bottom=df['close'][down], color='green', alpha=0.8, width=0.8)
        
        # 绘制影线
        ax.vlines(df.index[up], df['low'][up], df['high'][up], 
                 colors='red', alpha=0.8, linewidth=1)
        ax.vlines(df.index[down], df['low'][down], df['high'][down], 
                 colors='green', alpha=0.8, linewidth=1)
                 
    def _plot_turn_points(self, ax, turn_points: List[TurnPointEvent], df: pd.DataFrame):
        """绘制转折点标志"""
        for tp in turn_points:
            # 找到对应的数据点
            tp_time = pd.to_datetime(tp.timestamp)
            if tp_time in df.index:
                price = tp.price
                
                if tp.point_type == 'PEAK':
                    # 波峰用向下的三角形
                    ax.scatter(tp_time, price, marker='v', s=100, 
                             color='red', edgecolors='darkred', linewidth=2, 
                             zorder=5, label='波峰' if tp == turn_points[0] else "")
                    # 添加文字标注
                    ax.annotate(f'峰\n{price:.2f}', 
                               xy=(tp_time, price), 
                               xytext=(0, 20), textcoords='offset points',
                               ha='center', va='bottom',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7),
                               fontsize=8, color='white', weight='bold')
                else:  # TROUGH
                    # 波谷用向上的三角形
                    ax.scatter(tp_time, price, marker='^', s=100, 
                             color='blue', edgecolors='darkblue', linewidth=2, 
                             zorder=5, label='波谷' if tp == turn_points[0] else "")
                    # 添加文字标注
                    ax.annotate(f'谷\n{price:.2f}', 
                               xy=(tp_time, price), 
                               xytext=(0, -20), textcoords='offset points',
                               ha='center', va='top',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.7),
                               fontsize=8, color='white', weight='bold')
                               
    def _plot_support_resistance(self, ax, sr_levels: List[SupportResistanceLevel], df: pd.DataFrame):
        """绘制支撑阻力位"""
        if not sr_levels:
            return
            
        # 获取时间范围
        start_time = df.index[0]
        end_time = df.index[-1]
        
        for i, level in enumerate(sr_levels[:5]):  # 只显示前5个最强的
            color = 'red' if level.level_type == 'RESISTANCE' else 'green'
            alpha = min(0.3 + level.strength * 0.5, 0.8)
            
            # 绘制水平线
            ax.axhline(y=level.price, color=color, linestyle='--', 
                      alpha=alpha, linewidth=2, zorder=3)
            
            # 添加标注
            ax.text(end_time, level.price,
                   f'{level.level_type[0]}: {level.price:.2f} (强度:{level.strength:.2f})',
                   verticalalignment='center', horizontalalignment='right',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                   fontsize=8, color='white', weight='bold')

    def _plot_volume_divergence(self, ax, detector: TurnPointDetector, df: pd.DataFrame):
        """绘制量价背离信号"""
        # 获取量价信号
        volume_signals = detector.get_current_volume_signals()

        for signal in volume_signals:
            if signal.signal_type == 'VOLUME_DIVERGENCE':
                # 在图表上标注量价背离
                # 找到最近的时间点
                recent_time = df.index[-1]
                recent_price = df['close'].iloc[-1]

                ax.annotate('量价背离',
                           xy=(recent_time, recent_price),
                           xytext=(20, 20), textcoords='offset points',
                           ha='left', va='bottom',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='orange', alpha=0.8),
                           fontsize=10, color='white', weight='bold',
                           arrowprops=dict(arrowstyle='->', color='orange', lw=2))

    def _plot_volume(self, ax, df: pd.DataFrame):
        """绘制成交量"""
        # 计算涨跌
        up = df['close'] >= df['open']
        down = ~up

        # 绘制成交量柱状图
        ax.bar(df.index[up], df['volume'][up], color='red', alpha=0.6, width=0.8)
        ax.bar(df.index[down], df['volume'][down], color='green', alpha=0.6, width=0.8)

        # 添加成交量均线
        if len(df) >= 20:
            volume_ma20 = df['volume'].rolling(window=20).mean()
            ax.plot(df.index, volume_ma20, color='blue', linewidth=1, alpha=0.8, label='成交量20日均线')

    def _setup_chart_style(self, ax1, ax2, symbol: str, df: pd.DataFrame):
        """设置图表样式"""
        # 主图样式
        ax1.set_title(f'{symbol} 日K线图 - 转折点分析', fontsize=16, weight='bold', pad=20)
        ax1.set_ylabel('价格', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='y', labelsize=10)

        # 成交量图样式
        ax2.set_ylabel('成交量', fontsize=12)
        ax2.set_xlabel('日期', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='both', labelsize=10)

        # 设置x轴日期格式
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

        # 设置价格范围
        price_min = df[['low']].min().min()
        price_max = df[['high']].max().max()
        price_range = price_max - price_min
        ax1.set_ylim(price_min - price_range * 0.05, price_max + price_range * 0.15)

    def _add_legends_and_annotations(self, ax1, ax2, detector: TurnPointDetector):
        """添加图例和标注"""
        # 主图图例
        ax1.legend(loc='upper left', fontsize=10)

        # 成交量图图例
        ax2.legend(loc='upper left', fontsize=10)

        # 添加分析信息文本框
        analysis = detector.get_market_analysis()
        info_text = self._create_analysis_text(analysis)

        ax1.text(0.02, 0.98, info_text, transform=ax1.transAxes,
                verticalalignment='top', horizontalalignment='left',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8),
                fontsize=9)

    def _create_analysis_text(self, analysis: Dict[str, Any]) -> str:
        """创建分析信息文本"""
        lines = ['市场分析:']

        # 当前候选点信息
        if analysis.get('current_candidate'):
            candidate = analysis['current_candidate']
            lines.append(f"候选{candidate['point_type']}: {candidate['price']:.2f}")
            lines.append(f"持续天数: {candidate['keep_days']}天")

        # 成交量分析
        if analysis.get('volume_analysis'):
            vol_analysis = analysis['volume_analysis']
            lines.append(f"量比: {vol_analysis.get('volume_ratio', 0):.2f}")
            lines.append(f"成交量趋势: {vol_analysis.get('volume_trend', 0):.4f}")

        # 支撑阻力位数量
        sr_count = len(analysis.get('support_resistance_levels', []))
        lines.append(f"支撑阻力位: {sr_count}个")

        return '\n'.join(lines)


def create_enhanced_kline_chart(symbol: str,
                               market_data: List[MarketEvent],
                               turn_points: List[TurnPointEvent],
                               detector: TurnPointDetector,
                               save_path: Optional[str] = None) -> None:
    """
    创建增强K线图的便捷函数

    Args:
        symbol: 股票代码
        market_data: 市场数据列表
        turn_points: 转折点列表
        detector: 转折点检测器
        save_path: 保存路径
    """
    visualizer = KLineChartVisualizer()
    visualizer.create_kline_chart(
        market_data=market_data,
        turn_points=turn_points,
        detector=detector,
        symbol=symbol,
        save_path=save_path
    )
