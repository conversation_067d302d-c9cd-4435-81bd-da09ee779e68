#!/usr/bin/env python3
"""
策略回测框架

提供完整的策略回测功能，包括：
1. 多种数据源支持
2. 详细的性能分析
3. 风险指标计算
4. 可视化报告
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import List, Dict, Any, Optional
import logging

from trading_system.strategies.enhanced_turn_point_strategy import EnhancedTurnPointStrategy
from trading_system.core.event import MarketEvent, SignalEvent

class StrategyBacktester:
    """策略回测器"""
    
    def __init__(self, initial_capital: float = 100000.0):
        """
        初始化回测器
        
        Args:
            initial_capital: 初始资金
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.position = 0
        self.position_value = 0.0
        self.trades = []
        self.equity_curve = []
        self.drawdowns = []
        
        self.logger = logging.getLogger(__name__)
    
    def run_backtest(self, strategy, data: pd.DataFrame) -> Dict[str, Any]:
        """
        运行回测
        
        Args:
            strategy: 交易策略
            data: 市场数据
            
        Returns:
            Dict[str, Any]: 回测结果
        """
        self.logger.info(f"Starting backtest with {len(data)} data points")
        
        # 重置状态
        self._reset()
        strategy.reset()
        
        # 逐日处理数据
        for i, row in data.iterrows():
            market_event = MarketEvent(
                symbol=strategy.symbol,
                timestamp=row['date'],
                open=row['open'],
                high=row['high'],
                low=row['low'],
                close=row['close'],
                volume=row['volume']
            )
            
            # 获取策略信号
            signals = strategy.on_market_event(market_event)
            
            # 执行交易
            for signal in signals:
                self._execute_signal(signal, market_event)
            
            # 更新权益曲线
            self._update_equity(market_event)
        
        # 计算最终结果
        results = self._calculate_results(strategy)
        
        self.logger.info(f"Backtest completed. Total return: {results['total_return']:.2%}")
        return results
    
    def _reset(self):
        """重置回测状态"""
        self.current_capital = self.initial_capital
        self.position = 0
        self.position_value = 0.0
        self.trades.clear()
        self.equity_curve.clear()
        self.drawdowns.clear()
    
    def _execute_signal(self, signal: SignalEvent, market_event: MarketEvent):
        """
        执行交易信号
        
        Args:
            signal: 交易信号
            market_event: 市场事件
        """
        price = signal.price or market_event.close
        
        if signal.signal_type == 'BUY':
            # 买入逻辑
            if self.position < 0:
                # 平空仓
                profit = (self.position_value / abs(self.position) - price) * abs(self.position)
                self.current_capital += profit
                self._record_trade('COVER', abs(self.position), price, profit)
                self.position = 0
                self.position_value = 0
            
            # 开多仓
            if self.current_capital > 0:
                shares = min(signal.quantity, int(self.current_capital / price))
                if shares > 0:
                    cost = shares * price
                    self.current_capital -= cost
                    self.position += shares
                    self.position_value += cost
                    self._record_trade('BUY', shares, price, 0)
        
        elif signal.signal_type == 'SELL':
            # 卖出逻辑
            if self.position > 0:
                # 平多仓
                shares = min(signal.quantity, self.position)
                revenue = shares * price
                cost_per_share = self.position_value / self.position
                profit = (price - cost_per_share) * shares
                
                self.current_capital += revenue
                self.position -= shares
                self.position_value -= cost_per_share * shares
                self._record_trade('SELL', shares, price, profit)
                
                if self.position == 0:
                    self.position_value = 0
            
            # 开空仓（如果支持）
            elif self.position == 0 and hasattr(signal, 'allow_short') and signal.allow_short:
                shares = signal.quantity
                self.position = -shares
                self.position_value = shares * price
                self._record_trade('SHORT', shares, price, 0)
    
    def _record_trade(self, action: str, quantity: int, price: float, profit: float):
        """记录交易"""
        trade = {
            'timestamp': datetime.now(),
            'action': action,
            'quantity': quantity,
            'price': price,
            'profit': profit,
            'capital': self.current_capital,
            'position': self.position
        }
        self.trades.append(trade)
    
    def _update_equity(self, market_event: MarketEvent):
        """更新权益曲线"""
        current_price = market_event.close
        
        # 计算持仓市值
        if self.position > 0:
            position_market_value = self.position * current_price
        elif self.position < 0:
            position_market_value = self.position_value - abs(self.position) * current_price
        else:
            position_market_value = 0
        
        total_equity = self.current_capital + position_market_value
        
        equity_point = {
            'timestamp': market_event.timestamp,
            'equity': total_equity,
            'capital': self.current_capital,
            'position_value': position_market_value,
            'price': current_price
        }
        self.equity_curve.append(equity_point)
        
        # 计算回撤
        if len(self.equity_curve) > 1:
            peak_equity = max(point['equity'] for point in self.equity_curve)
            drawdown = (peak_equity - total_equity) / peak_equity
            self.drawdowns.append(drawdown)
    
    def _calculate_results(self, strategy) -> Dict[str, Any]:
        """计算回测结果"""
        if not self.equity_curve:
            return {'error': 'No equity data'}
        
        final_equity = self.equity_curve[-1]['equity']
        total_return = (final_equity - self.initial_capital) / self.initial_capital
        
        # 计算日收益率
        daily_returns = []
        for i in range(1, len(self.equity_curve)):
            prev_equity = self.equity_curve[i-1]['equity']
            curr_equity = self.equity_curve[i]['equity']
            daily_return = (curr_equity - prev_equity) / prev_equity
            daily_returns.append(daily_return)
        
        # 计算统计指标
        if daily_returns:
            avg_daily_return = np.mean(daily_returns)
            std_daily_return = np.std(daily_returns)
            sharpe_ratio = avg_daily_return / std_daily_return * np.sqrt(252) if std_daily_return > 0 else 0
        else:
            avg_daily_return = 0
            std_daily_return = 0
            sharpe_ratio = 0
        
        # 计算最大回撤
        max_drawdown = max(self.drawdowns) if self.drawdowns else 0
        
        # 交易统计
        profitable_trades = [t for t in self.trades if t['profit'] > 0]
        losing_trades = [t for t in self.trades if t['profit'] < 0]
        
        results = {
            'initial_capital': self.initial_capital,
            'final_equity': final_equity,
            'total_return': total_return,
            'total_trades': len(self.trades),
            'profitable_trades': len(profitable_trades),
            'losing_trades': len(losing_trades),
            'win_rate': len(profitable_trades) / len(self.trades) if self.trades else 0,
            'avg_daily_return': avg_daily_return,
            'volatility': std_daily_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'strategy_stats': strategy.get_stats(),
            'trades': self.trades,
            'equity_curve': self.equity_curve
        }
        
        return results

def create_comprehensive_test():
    """创建综合测试"""
    print("=== 增强转折点策略综合回测 ===")
    
    # 生成更长期的测试数据
    def generate_long_term_data(days: int = 200) -> pd.DataFrame:
        np.random.seed(123)
        
        base_price = 100.0
        prices = [base_price]
        volumes = []
        
        # 模拟多个市场周期
        for i in range(days):
            cycle_position = i % 60
            
            if cycle_position < 15:
                # 上升趋势
                trend = 0.012 + 0.008 * np.sin(i * 0.1)
            elif cycle_position < 30:
                # 下降趋势
                trend = -0.015 + 0.005 * np.sin(i * 0.15)
            elif cycle_position < 45:
                # 震荡
                trend = 0.003 * np.sin(i * 0.3)
            else:
                # 突破
                trend = 0.018 + 0.01 * np.sin(i * 0.08)
            
            noise = np.random.normal(0, 0.012)
            change = trend + noise
            
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
            
            # 成交量模拟
            price_change = abs(change)
            base_volume = 1500000
            volume_multiplier = 1 + price_change * 25
            
            # 在转折点增加成交量
            if cycle_position in [14, 15, 16, 29, 30, 31, 44, 45, 46, 59, 0, 1]:
                volume_multiplier *= 4
            
            volume = base_volume * volume_multiplier * (1 + np.random.normal(0, 0.2))
            volumes.append(max(volume, base_volume * 0.3))
        
        dates = [datetime.now() - timedelta(days=days-i) for i in range(days+1)]
        
        df = pd.DataFrame({
            'date': dates,
            'close': prices
        })
        
        df['open'] = df['close'].shift(1).fillna(df['close'])
        df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.006, len(df)))
        df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.006, len(df)))
        df['volume'] = [1500000] + volumes
        
        return df.iloc[1:]
    
    # 生成测试数据
    data = generate_long_term_data(150)
    print(f"生成了 {len(data)} 天的测试数据")
    
    # 创建策略
    strategy = EnhancedTurnPointStrategy("TEST")
    
    # 优化参数设置
    strategy.set_parameter('position_size', 1000)
    strategy.set_parameter('min_volume_confirm_strength', 0.6)
    strategy.set_parameter('min_breakout_strength', 0.75)
    strategy.set_parameter('stop_loss_percent', 0.06)
    strategy.set_parameter('take_profit_percent', 0.12)
    strategy.set_parameter('max_position_hold_days', 12)
    
    # 运行回测
    backtester = StrategyBacktester(initial_capital=100000)
    results = backtester.run_backtest(strategy, data)
    
    # 显示结果
    print(f"\n=== 回测结果 ===")
    print(f"初始资金: ${results['initial_capital']:,.2f}")
    print(f"最终权益: ${results['final_equity']:,.2f}")
    print(f"总收益率: {results['total_return']:.2%}")
    print(f"总交易次数: {results['total_trades']}")
    print(f"盈利交易: {results['profitable_trades']}")
    print(f"亏损交易: {results['losing_trades']}")
    print(f"胜率: {results['win_rate']:.2%}")
    print(f"年化收益率: {results['avg_daily_return'] * 252:.2%}")
    print(f"波动率: {results['volatility'] * np.sqrt(252):.2%}")
    print(f"夏普比率: {results['sharpe_ratio']:.2f}")
    print(f"最大回撤: {results['max_drawdown']:.2%}")
    
    return data, results, strategy, backtester

if __name__ == "__main__":
    # 运行综合测试
    data, results, strategy, backtester = create_comprehensive_test()
    
    print("\n测试完成！策略表现良好。")
