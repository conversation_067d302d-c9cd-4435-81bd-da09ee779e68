#!/usr/bin/env python3
"""
测试增强策略在main.py中的集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
import pandas as pd
from datetime import datetime
from typing import Dict, Any

# 导入增强策略相关模块
from trading_system.core import EventEngine, MarketEvent, SignalEvent, OrderEvent, FillEvent, TurnPointEvent
from trading_system.data import HistoricalDataProvider
from trading_system.analysis import IndicatorCalculator, TurnPointDetector
from trading_system.strategies import EnhancedTurnPointStrategy
from trading_system.execution import BacktestExecutionHandler
from trading_system.portfolio import PortfolioManager

class SimpleTradingSystem:
    """简化的交易系统用于测试"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = self._setup_logging()
        
        # 核心组件
        self.event_engine = EventEngine()
        self.data_provider = None
        self.indicator_calculator = IndicatorCalculator()
        self.turn_point_detector = TurnPointDetector(
            min_keep_days=config.get('min_keep_days', 2),
            min_extent_percent=config.get('min_extent_percent', 2.0),
            volume_window=15,
            volume_threshold=1.3,
            sr_sensitivity=0.02
        )
        self.strategy = None
        self.execution_handler = BacktestExecutionHandler(
            slippage_pct=config.get('slippage_pct', 0.001)
        )
        self.portfolio_manager = PortfolioManager(
            initial_cash=config.get('initial_cash', 100000)
        )
        
        # 运行状态
        self.is_running = False
        self.market_data_history = []
        self.turn_points = []
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def initialize(self):
        """初始化系统组件"""
        self.logger.info("Initializing enhanced trading system...")
        
        # 1. 初始化数据提供者
        self.data_provider = HistoricalDataProvider(
            symbol=self.config['symbol'],
            start_date=self.config['start_date'],
            end_date=self.config['end_date']
        )
        
        # 2. 初始化增强策略
        self.strategy = EnhancedTurnPointStrategy(self.config['symbol'])
        
        # 设置优化后的策略参数
        self.strategy.set_parameter('position_size', self.config.get('position_size', 100))
        self.strategy.set_parameter('min_volume_confirm_strength', self.config.get('min_volume_confirm_strength', 0.4))
        self.strategy.set_parameter('min_breakout_strength', self.config.get('min_breakout_strength', 0.6))
        self.strategy.set_parameter('stop_loss_percent', self.config.get('stop_loss_percent', 0.04))
        self.strategy.set_parameter('take_profit_percent', self.config.get('take_profit_percent', 0.15))
        self.strategy.set_parameter('max_position_hold_days', self.config.get('max_position_hold_days', 10))
        
        # 3. 创建默认指标
        self.indicator_calculator.create_default_indicators()
        
        # 4. 注册事件处理器
        self._register_event_handlers()
        
        self.logger.info("Enhanced system initialized successfully")
    
    def _register_event_handlers(self):
        """注册事件处理器"""
        self.event_engine.register_handler(MarketEvent, self._on_market_event)
        self.event_engine.register_handler(TurnPointEvent, self._on_turn_point_event)
        self.event_engine.register_handler(SignalEvent, self._on_signal_event)
        self.event_engine.register_handler(OrderEvent, self._on_order_event)
        self.event_engine.register_handler(FillEvent, self._on_fill_event)
    
    def _on_market_event(self, event: MarketEvent):
        """处理市场数据事件"""
        # 1. 计算技术指标
        indicators = self.indicator_calculator.calculate_all(event)
        event.indicators.update(indicators)
        
        # 2. 检测转折点
        turn_point_event = self.turn_point_detector.process_market_event(event)
        if turn_point_event:
            self.turn_points.append(turn_point_event)
            self.event_engine.put_event(turn_point_event)
        
        # 3. 策略处理
        signals = self.strategy.on_market_event(event)
        for signal in signals:
            self.event_engine.put_event(signal)
        
        # 4. 更新投资组合市值
        self.portfolio_manager.update_market_value(event)
        
        # 5. 更新执行处理器的市场数据
        self.execution_handler.update_market_data(event)
        
        # 6. 保存历史数据
        self.market_data_history.append(event)
    
    def _on_turn_point_event(self, event: TurnPointEvent):
        """处理转折点事件"""
        signals = self.strategy.on_turn_point_event(event)
        for signal in signals:
            self.event_engine.put_event(signal)
    
    def _on_signal_event(self, event: SignalEvent):
        """处理交易信号事件"""
        orders = self.execution_handler.process_signal(event)
        for order in orders:
            self.event_engine.put_event(order)
    
    def _on_order_event(self, event: OrderEvent):
        """处理订单事件"""
        fill_event = self.execution_handler.execute_order(event)
        if fill_event:
            self.event_engine.put_event(fill_event)
    
    def _on_fill_event(self, event: FillEvent):
        """处理成交事件"""
        self.portfolio_manager.process_fill_event(event)
    
    def run_backtest(self):
        """运行回测"""
        self.logger.info("Starting enhanced strategy backtest...")
        self.is_running = True
        
        # 启动事件引擎
        self.event_engine.start()
        
        try:
            # 获取数据流并处理
            data_count = 0
            for market_event in self.data_provider.get_data_stream():
                if not self.is_running:
                    break
                
                # 将市场数据事件放入队列
                self.event_engine.put_event(market_event)
                data_count += 1
                
                if data_count % 50 == 0:
                    self.logger.info(f"已处理 {data_count} 条 market events")
            
            # 等待所有事件处理完成
            self.event_engine.wait_for_completion(timeout=30)
            
            self.logger.info(f"回测结束. 处理完成 {data_count} 条 market events")
            
        except Exception as e:
            self.logger.error(f"Error during backtest: {e}")
            raise
        finally:
            # 停止事件引擎
            self.event_engine.stop()
            self.is_running = False
    
    def show_results(self):
        """显示回测结果"""
        print("\n" + "=" * 70)
        print("增强转折点策略回测结果")
        print("=" * 70)
        
        # 显示策略统计
        strategy_stats = self.strategy.get_stats()
        print(f"策略名称: {strategy_stats['name']}")
        print(f"交易标的: {strategy_stats['symbol']}")
        print(f"当前持仓: {strategy_stats['current_position']}")
        print(f"生成信号数: {strategy_stats['signals_count']}")
        
        # 显示信号统计
        if 'signal_stats' in strategy_stats:
            signal_stats = strategy_stats['signal_stats']
            print(f"\n信号统计:")
            print(f"  总信号数: {signal_stats['total_signals']}")
            print(f"  买入信号: {signal_stats['buy_signals']}")
            print(f"  卖出信号: {signal_stats['sell_signals']}")
            print(f"  量价确认: {signal_stats['volume_confirmed']}")
            print(f"  支撑阻力确认: {signal_stats['sr_confirmed']}")
            print(f"  过滤信号: {signal_stats['filtered_out']}")
        
        # 显示检测器统计
        if 'detector_stats' in strategy_stats:
            detector_stats = strategy_stats['detector_stats']
            print(f"\n转折点检测统计:")
            print(f"  确认转折点: {detector_stats['confirmed_points']}")
            print(f"  支撑阻力位: {detector_stats['sr_levels']}")
            print(f"  量价信号: {detector_stats['volume_signals']}")
        
        # 显示投资组合表现
        performance_stats = self.portfolio_manager.get_performance_stats()
        print(f"\n投资组合表现:")
        print(f"  初始资金: ¥{performance_stats['initial_cash']:,.2f}")
        print(f"  最终权益: ¥{performance_stats['current_value']:,.2f}")
        print(f"  总收益: ¥{performance_stats['current_value'] - performance_stats['initial_cash']:,.2f}")
        print(f"  总收益率: {performance_stats['total_return']:.2%}")
        print(f"  最大回撤: {performance_stats['max_drawdown']:.2%}")
        print(f"  交易次数: {performance_stats['total_trades']}")
        print(f"  胜率: {performance_stats['win_rate']:.1f}%")
        
        # 显示当前市场分析
        if hasattr(self.strategy, 'get_market_analysis'):
            analysis = self.strategy.get_market_analysis()
            if analysis.get('volume_analysis'):
                va = analysis['volume_analysis']
                print(f"\n当前市场分析:")
                print(f"  当前量比: {va.get('volume_ratio', 0):.2f}")
                print(f"  成交量趋势: {va.get('volume_trend', 0):.4f}")
            
            if analysis.get('support_resistance_levels'):
                sr_levels = analysis['support_resistance_levels'][:3]
                print(f"  主要支撑阻力位:")
                for i, level in enumerate(sr_levels):
                    print(f"    {i+1}. {level['level_type']} {level['price']:.2f} (强度: {level['strength']:.2f})")
        
        print("=" * 70)

def create_enhanced_config() -> Dict[str, Any]:
    """创建增强策略配置"""
    return {
        # 基本配置
        'symbol': '003021',
        'start_date': '20250101',
        'end_date': '20250630',
        'initial_cash': 100000,
        'slippage_pct': 0.001,
        
        # 转折点检测器配置
        'min_keep_days': 2,
        'min_extent_percent': 2.0,
        
        # 增强策略配置 (优化后的参数)
        'position_size': 100,
        'min_volume_confirm_strength': 0.4,
        'min_breakout_strength': 0.6,
        'stop_loss_percent': 0.04,
        'take_profit_percent': 0.15,
        'max_position_hold_days': 10
    }

def main():
    """主函数"""
    print("=" * 70)
    print("增强转折点策略测试")
    print("基于量价分析的智能交易系统")
    print("=" * 70)
    
    # 创建配置
    config = create_enhanced_config()
    
    # 显示配置信息
    print(f"交易标的: {config['symbol']}")
    print(f"回测期间: {config['start_date']} - {config['end_date']}")
    print(f"初始资金: ¥{config['initial_cash']:,}")
    print(f"策略类型: 增强转折点策略 (优化参数)")
    print("-" * 70)
    
    # 创建交易系统
    trading_system = SimpleTradingSystem(config)
    
    try:
        # 初始化系统
        trading_system.initialize()
        
        # 运行回测
        trading_system.run_backtest()
        
        # 显示结果
        trading_system.show_results()
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n系统运行出错: {e}")
        trading_system.logger.error(f"System error: {e}", exc_info=True)
    finally:
        if trading_system.is_running:
            trading_system.event_engine.stop()

if __name__ == "__main__":
    main()
