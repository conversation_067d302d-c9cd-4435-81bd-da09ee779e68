"""
可视化工具

提供交易结果的图表展示功能。
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime
import numpy as np

from ..portfolio.manager import PortfolioManager, PortfolioSnapshot
from ..core.event import TurnPointEvent, FillEvent


class TradingVisualizer:
    """
    交易可视化工具
    
    提供各种图表展示交易结果和分析。
    """
    
    def __init__(self, figsize: tuple = (15, 12)):
        """
        初始化可视化工具
        
        Args:
            figsize: 图表大小
        """
        self.figsize = figsize
        plt.style.use('default')
        
        # 设置中文字体（如果需要）
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def plot_trading_results(self, portfolio_manager: PortfolioManager,
                           market_data: pd.DataFrame,
                           turn_points: List[TurnPointEvent] = None,
                           save_path: Optional[str] = None):
        """
        绘制完整的交易结果图表
        
        Args:
            portfolio_manager: 投资组合管理器
            market_data: 市场数据
            turn_points: 转折点列表
            save_path: 保存路径
        """
        fig, axes = plt.subplots(4, 1, figsize=self.figsize, sharex=True)
        
        # 准备数据
        portfolio_history = portfolio_manager.portfolio_history
        trade_history = portfolio_manager.trade_history
        
        if not portfolio_history:
            print("No portfolio history available")
            return
        
        # 转换为DataFrame便于处理
        portfolio_df = pd.DataFrame([
            {
                'timestamp': snapshot.timestamp,
                'total_value': snapshot.total_value,
                'cash': snapshot.cash,
                'daily_pnl': snapshot.daily_pnl
            }
            for snapshot in portfolio_history
        ])
        
        # 1. 价格图表和转折点
        self._plot_price_and_signals(axes[0], market_data, turn_points, trade_history)
        
        # 2. 投资组合价值
        self._plot_portfolio_value(axes[1], portfolio_df)
        
        # 3. 持仓和现金
        self._plot_positions_and_cash(axes[2], portfolio_df, portfolio_manager)
        
        # 4. 日收益
        self._plot_daily_returns(axes[3], portfolio_df)
        
        # 设置整体格式
        plt.tight_layout()
        plt.suptitle('Trading System Results', fontsize=16, y=0.98)
        
        # 保存或显示
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Chart saved to {save_path}")
        else:
            plt.show()
    
    def _plot_price_and_signals(self, ax, market_data: pd.DataFrame, 
                               turn_points: List[TurnPointEvent],
                               trade_history: List[Dict]):
        """绘制价格图表和交易信号"""
        # 绘制价格线
        ax.plot(market_data['date'], market_data['close'], 
                label='Price', color='black', linewidth=1)
        
        # 标记转折点
        if turn_points:
            peak_dates = []
            peak_prices = []
            trough_dates = []
            trough_prices = []
            
            for point in turn_points:
                if point.point_type == 'PEAK':
                    peak_dates.append(point.timestamp)
                    peak_prices.append(point.price)
                else:  # TROUGH
                    trough_dates.append(point.timestamp)
                    trough_prices.append(point.price)
            
            if peak_dates:
                ax.scatter(peak_dates, peak_prices, marker='v', 
                          color='red', s=50, label='Peak', zorder=5)
            if trough_dates:
                ax.scatter(trough_dates, trough_prices, marker='^', 
                          color='green', s=50, label='Trough', zorder=5)
        
        # 标记交易信号
        if trade_history:
            buy_dates = [datetime.fromisoformat(str(t['timestamp'])) if isinstance(t['timestamp'], str) 
                        else t['timestamp'] for t in trade_history if t['direction'] == 'BUY']
            buy_prices = [t['price'] for t in trade_history if t['direction'] == 'BUY']
            sell_dates = [datetime.fromisoformat(str(t['timestamp'])) if isinstance(t['timestamp'], str) 
                         else t['timestamp'] for t in trade_history if t['direction'] == 'SELL']
            sell_prices = [t['price'] for t in trade_history if t['direction'] == 'SELL']
            
            if buy_dates:
                ax.scatter(buy_dates, buy_prices, marker='^', 
                          color='blue', s=80, label='Buy', zorder=6)
            if sell_dates:
                ax.scatter(sell_dates, sell_prices, marker='v', 
                          color='orange', s=80, label='Sell', zorder=6)
        
        ax.set_title('Price Chart with Trading Signals')
        ax.set_ylabel('Price')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_portfolio_value(self, ax, portfolio_df: pd.DataFrame):
        """绘制投资组合价值"""
        ax.plot(portfolio_df['timestamp'], portfolio_df['total_value'], 
                color='green', linewidth=2, label='Portfolio Value')
        
        ax.set_title('Portfolio Value')
        ax.set_ylabel('Value')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加收益率标注
        if len(portfolio_df) > 1:
            initial_value = portfolio_df['total_value'].iloc[0]
            final_value = portfolio_df['total_value'].iloc[-1]
            total_return = (final_value - initial_value) / initial_value * 100
            
            ax.text(0.02, 0.95, f'Total Return: {total_return:.2f}%', 
                   transform=ax.transAxes, fontsize=10,
                   bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))
    
    def _plot_positions_and_cash(self, ax, portfolio_df: pd.DataFrame, 
                                portfolio_manager: PortfolioManager):
        """绘制持仓和现金"""
        # 现金曲线
        ax.plot(portfolio_df['timestamp'], portfolio_df['cash'], 
                color='orange', linewidth=2, label='Cash')
        
        # 如果有持仓数据，绘制持仓价值
        if portfolio_manager.positions:
            # 计算持仓价值历史（简化处理）
            position_values = []
            for snapshot in portfolio_manager.portfolio_history:
                total_position_value = sum(pos.market_value for pos in snapshot.positions.values())
                position_values.append(total_position_value)
            
            ax.plot(portfolio_df['timestamp'], position_values, 
                    color='purple', linewidth=2, label='Position Value')
        
        ax.set_title('Cash and Position Value')
        ax.set_ylabel('Value')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_daily_returns(self, ax, portfolio_df: pd.DataFrame):
        """绘制日收益"""
        if len(portfolio_df) > 1:
            # 计算日收益率
            daily_returns = portfolio_df['total_value'].pct_change() * 100
            
            # 绘制收益率
            colors = ['red' if x < 0 else 'green' for x in daily_returns]
            ax.bar(portfolio_df['timestamp'], daily_returns, 
                   color=colors, alpha=0.7, width=1)
            
            # 添加零线
            ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            
            ax.set_title('Daily Returns')
            ax.set_ylabel('Return (%)')
            ax.set_xlabel('Date')
            ax.grid(True, alpha=0.3)
            
            # 格式化x轴日期
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def plot_performance_metrics(self, portfolio_manager: PortfolioManager,
                                save_path: Optional[str] = None):
        """
        绘制绩效指标图表
        
        Args:
            portfolio_manager: 投资组合管理器
            save_path: 保存路径
        """
        stats = portfolio_manager.get_performance_stats()
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 1. 收益概览
        ax1 = axes[0, 0]
        categories = ['Initial', 'Final', 'Profit/Loss']
        values = [stats['initial_cash'], stats['current_value'], 
                 stats['current_value'] - stats['initial_cash']]
        colors = ['blue', 'green' if values[2] >= 0 else 'red', 
                 'green' if values[2] >= 0 else 'red']
        
        bars = ax1.bar(categories, values, color=colors, alpha=0.7)
        ax1.set_title('Portfolio Overview')
        ax1.set_ylabel('Value')
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:,.0f}', ha='center', va='bottom')
        
        # 2. 关键指标
        ax2 = axes[0, 1]
        metrics = ['Total Return (%)', 'Max Drawdown (%)', 'Win Rate (%)']
        metric_values = [stats['total_return_pct'], 
                        stats['max_drawdown'] * 100, 
                        stats['win_rate']]
        
        bars = ax2.barh(metrics, metric_values, 
                       color=['green' if x >= 0 else 'red' for x in metric_values])
        ax2.set_title('Key Metrics')
        ax2.set_xlabel('Percentage')
        
        # 3. 交易统计
        ax3 = axes[1, 0]
        trade_labels = ['Total Trades', 'Winning Trades', 'Losing Trades']
        trade_counts = [stats['total_trades'], stats['winning_trades'], 
                       stats['total_trades'] - stats['winning_trades']]
        
        # 检查是否有交易数据
        if trade_counts[0] > 0:
            ax3.pie(trade_counts[1:], labels=trade_labels[1:], autopct='%1.1f%%',
                   colors=['green', 'red'], startangle=90)
            ax3.set_title(f'Trade Distribution (Total: {trade_counts[0]})')
        else:
            ax3.text(0.5, 0.5, 'No Trades\nExecuted', ha='center', va='center',
                    transform=ax3.transAxes, fontsize=14, color='gray')
            ax3.set_title('Trade Distribution (Total: 0)')
        
        # 4. 资产分布
        ax4 = axes[1, 1]
        asset_labels = ['Cash', 'Positions']
        total_position_value = sum(pos.market_value for pos in portfolio_manager.positions.values())
        asset_values = [stats['cash'], total_position_value]
        
        if sum(asset_values) > 0:
            ax4.pie(asset_values, labels=asset_labels, autopct='%1.1f%%',
                   colors=['orange', 'purple'], startangle=90)
        ax4.set_title('Asset Allocation')
        
        plt.tight_layout()
        plt.suptitle('Performance Metrics', fontsize=16, y=0.98)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Performance chart saved to {save_path}")
        else:
            plt.show()
    
    def print_performance_report(self, portfolio_manager: PortfolioManager):
        """
        打印绩效报告
        
        Args:
            portfolio_manager: 投资组合管理器
        """
        stats = portfolio_manager.get_performance_stats()
        
        print(f"\n{'=' * 80}")
        print(f"{'TRADING SYSTEM PERFORMANCE REPORT':^80}")
        print(f"{'=' * 80}")

        print(f"\n📊 Portfolio Overview:")
        print(f"  Initial Capital:     ¥{stats['initial_cash']:>12,.2f}")
        print(f"  Final Value:         ¥{stats['current_value']:>12,.2f}")
        print(f"  Total Profit/Loss:   ¥{stats['current_value'] - stats['initial_cash']:>12,.2f}")
        print(f"  Total Return:        {stats['total_return_pct']:>12.2f}%")

        print(f"\n📈 Risk Metrics:")
        print(f"  Maximum Drawdown:    {stats['max_drawdown'] * 100:>12.2f}%")

        print(f"\n💼 Trading Statistics:")
        print(f"  Total Trades:        {stats['total_trades']:>12}")
        print(f"  Winning Trades:      {stats['winning_trades']:>12}")
        print(f"  Win Rate:            {stats['win_rate']:>12.2f}%")
        print(f"  Total Commission:    ¥{stats['total_commission']:>12,.2f}")

        print(f"\n💰 Current Holdings:")
        print(f"  Cash:                ¥{stats['cash']:>12,.2f}")
        print(f"  Active Positions:    {stats['positions_count']:>12}")

        # 显示具体持仓
        positions = portfolio_manager.get_all_positions()
        active_positions = {k: v for k, v in positions.items() if v.quantity != 0}

        if active_positions:
            print(f"\n📋 Position Details:")
            for symbol, position in active_positions.items():
                pnl_status = "📈" if position.unrealized_pnl >= 0 else "📉"
                print(f"  {symbol}: {position.quantity:>8} shares @ ¥{position.avg_cost:>8.2f} "
                      f"(Current: ¥{position.last_price:.2f}) {pnl_status} ¥{position.unrealized_pnl:>8.2f}")

        # 显示详细交易记录
        self.print_detailed_trading_history(portfolio_manager)

        print(f"\n{'=' * 80}")

    def print_detailed_trading_history(self, portfolio_manager):
        """打印详细的交易历史记录"""
        trade_summary = portfolio_manager.get_detailed_trade_summary()
        trade_history = portfolio_manager.get_trade_history()

        if not trade_history:
            print(f"\n📝 Trading History: No trades executed")
            return

        print(f"\n📝 Detailed Trading History:")
        print(f"{'=' * 80}")

        # 交易汇总
        print(f"\n📊 Trading Summary:")
        print(f"  Total Transactions:  {trade_summary['total_trades']:>12}")
        print(f"  Buy Orders:          {trade_summary['buy_trades']:>12}")
        print(f"  Sell Orders:         {trade_summary['sell_trades']:>12}")
        print(f"  Completed Trades:    {trade_summary['completed_trades']:>12}")
        print(f"  Total Volume:        ¥{trade_summary['total_volume']:>12,.2f}")
        print(f"  Total Commission:    ¥{trade_summary['total_commission']:>12,.2f}")

        if trade_summary['completed_trades'] > 0:
            print(f"  Average Hold Days:   {trade_summary['avg_hold_days']:>12.1f}")
            print(f"  Average Profit:      ¥{trade_summary['avg_profit']:>12,.2f}")
            print(f"  Average Loss:        ¥{trade_summary['avg_loss']:>12,.2f}")

        # 最佳和最差交易
        if trade_summary['best_trade']:
            best = trade_summary['best_trade']
            print(f"\n🏆 Best Trade:")
            print(f"  Date: {best['buy_date'].strftime('%Y-%m-%d')} → {best['sell_date'].strftime('%Y-%m-%d')}")
            print(f"  Price: ¥{best['buy_price']:.2f} → ¥{best['sell_price']:.2f}")
            print(f"  Profit: ¥{best['profit']:,.2f} ({best['return_pct']:+.2f}%)")
            print(f"  Hold: {best['hold_days']} days")

        if trade_summary['worst_trade']:
            worst = trade_summary['worst_trade']
            print(f"\n📉 Worst Trade:")
            print(f"  Date: {worst['buy_date'].strftime('%Y-%m-%d')} → {worst['sell_date'].strftime('%Y-%m-%d')}")
            print(f"  Price: ¥{worst['buy_price']:.2f} → ¥{worst['sell_price']:.2f}")
            print(f"  Loss: ¥{worst['profit']:,.2f} ({worst['return_pct']:+.2f}%)")
            print(f"  Hold: {worst['hold_days']} days")

        # 详细交易记录表
        print(f"\n📋 Transaction Log:")
        print(f"{'No.':<4} {'Date':<12} {'Type':<4} {'Qty':<6} {'Price':<10} {'Amount':<12} {'Commission':<10} {'Cash After':<12}")
        print(f"{'-' * 80}")

        for i, trade in enumerate(trade_history, 1):
            trade_type = "BUY" if trade['direction'] == 'BUY' else "SELL"
            amount = trade['quantity'] * trade['price']
            date_str = trade['timestamp'].strftime('%Y-%m-%d')

            # 根据交易类型设置颜色标识
            type_icon = "🟢" if trade['direction'] == 'BUY' else "🔴"

            print(f"{i:<4} {date_str:<12} {type_icon}{trade_type:<3} {trade['quantity']:<6} "
                  f"¥{trade['price']:<9.2f} ¥{amount:<11,.2f} ¥{trade['commission']:<9.2f} ¥{trade['cash_after']:<11,.2f}")

        # 完整交易对分析
        if trade_summary['completed_trades'] > 0:
            print(f"\n💹 Completed Trade Pairs:")
            print(f"{'No.':<4} {'Buy Date':<12} {'Sell Date':<12} {'Buy Price':<10} {'Sell Price':<10} {'P&L':<12} {'Return':<8} {'Days':<5}")
            print(f"{'-' * 80}")

            all_completed = (trade_summary['profitable_trades'] +
                           trade_summary['losing_trades'] +
                           trade_summary['break_even_trades'])

            # 按买入日期排序
            all_completed.sort(key=lambda x: x['buy_date'])

            for i, trade in enumerate(all_completed, 1):
                buy_date = trade['buy_date'].strftime('%Y-%m-%d')
                sell_date = trade['sell_date'].strftime('%Y-%m-%d')
                profit_icon = "💰" if trade['profit'] > 0 else "💸" if trade['profit'] < 0 else "⚖️"

                print(f"{i:<4} {buy_date:<12} {sell_date:<12} ¥{trade['buy_price']:<9.2f} "
                      f"¥{trade['sell_price']:<9.2f} {profit_icon}¥{trade['profit']:<10,.2f} "
                      f"{trade['return_pct']:>+6.2f}% {trade['hold_days']:<5}")

        print(f"{'-' * 80}")
