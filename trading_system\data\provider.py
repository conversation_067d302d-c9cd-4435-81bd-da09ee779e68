"""
数据提供者模块

提供统一的数据接口，支持历史数据回测和实时数据交易。
通过生成器模式实现流式数据处理，确保无未来数据泄露。
"""

import pandas as pd
import akshare as ak
from abc import ABC, abstractmethod
from typing import Iterator, Optional, Dict, Any
from datetime import datetime, timedelta
import time
import logging

from ..core.event import MarketEvent


class DataProvider(ABC):
    """
    数据提供者抽象基类
    
    定义数据提供者的统一接口，支持流式数据生成。
    """
    
    def __init__(self, symbol: str):
        """
        初始化数据提供者
        
        Args:
            symbol: 股票代码
        """
        self.symbol = symbol
        self.logger = logging.getLogger(__name__)
    
    @abstractmethod
    def get_data_stream(self) -> Iterator[MarketEvent]:
        """
        获取数据流
        
        Returns:
            Iterator[MarketEvent]: 市场数据事件流
        """
        pass
    
    @abstractmethod
    def is_market_open(self) -> bool:
        """
        检查市场是否开放
        
        Returns:
            bool: 市场是否开放
        """
        pass


class HistoricalDataProvider(DataProvider):
    """
    历史数据提供者
    
    用于回测，按时间顺序逐条提供历史K线数据。
    """
    
    def __init__(self, symbol: str, start_date: str, end_date: str, 
                 period: str = "daily"):
        """
        初始化历史数据提供者
        
        Args:
            symbol: 股票代码
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            period: 数据周期 (daily, weekly, monthly)
        """
        super().__init__(symbol)
        self.start_date = start_date
        self.end_date = end_date
        self.period = period
        self.data: Optional[pd.DataFrame] = None
        self._load_data()
    
    def _load_data(self):
        """
        加载历史数据
        """
        try:
            self.logger.info(f"Loading historical data for {self.symbol}")
            
            # 使用akshare获取历史数据
            df = ak.stock_zh_a_hist(
                symbol=self.symbol,
                period=self.period,
                start_date=self.start_date,
                end_date=self.end_date
            )
            
            # 规范列名
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume'
            })
            
            # 转换日期格式并排序
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date').reset_index(drop=True)

            # 增加数据截断逻辑，确保回测确定性
            end_date_dt = pd.to_datetime(self.end_date)
            df = df[df['date'] <= end_date_dt]
            
            # 计算平均成交量
            df['avg_vol'] = df['volume'].rolling(window=20, min_periods=1).mean()
            
            self.data = df[['date', 'open', 'high', 'low', 'close', 'volume', 'avg_vol']]
            self.logger.info(f"Loaded and truncated {len(self.data)} records for {self.symbol} up to {self.end_date}")
            
        except Exception as e:
            self.logger.error(f"Failed to load data for {self.symbol}: {e}")
            raise
    
    def get_data_stream(self) -> Iterator[MarketEvent]:
        """
        获取历史数据流
        
        Yields:
            MarketEvent: 历史市场数据事件
        """
        if self.data is None or self.data.empty:
            self.logger.warning(f"No data available for {self.symbol}")
            return
        
        for _, row in self.data.iterrows():
            event = MarketEvent(
                symbol=self.symbol,
                timestamp=row['date'],
                open=float(row['open']),
                high=float(row['high']),
                low=float(row['low']),
                close=float(row['close']),
                volume=float(row['volume']),
                metadata={
                    'avg_volume': float(row['avg_vol']),
                    'data_source': 'historical'
                }
            )
            yield event
    
    def is_market_open(self) -> bool:
        """
        历史数据模式下始终返回True
        
        Returns:
            bool: True
        """
        return True
    
    def get_data_info(self) -> Dict[str, Any]:
        """
        获取数据信息
        
        Returns:
            Dict: 数据信息
        """
        if self.data is None:
            return {}
        
        return {
            'symbol': self.symbol,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'period': self.period,
            'total_records': len(self.data),
            'date_range': {
                'start': self.data['date'].min(),
                'end': self.data['date'].max()
            }
        }


class RealTimeDataProvider(DataProvider):
    """
    实时数据提供者
    
    用于实盘交易，提供实时K线数据流。
    """
    
    def __init__(self, symbol: str, interval: int = 60):
        """
        初始化实时数据提供者
        
        Args:
            symbol: 股票代码
            interval: 数据更新间隔（秒）
        """
        super().__init__(symbol)
        self.interval = interval
        self.last_update = None
        self._running = False
    
    def get_data_stream(self) -> Iterator[MarketEvent]:
        """
        获取实时数据流
        
        Yields:
            MarketEvent: 实时市场数据事件
        """
        self._running = True
        
        while self._running and self.is_market_open():
            try:
                # 获取最新数据
                kline_data = self._fetch_latest_data()
                
                if kline_data:
                    event = MarketEvent(
                        symbol=self.symbol,
                        timestamp=kline_data['date'],
                        open=kline_data['open'],
                        high=kline_data['high'],
                        low=kline_data['low'],
                        close=kline_data['close'],
                        volume=kline_data['volume'],
                        metadata={
                            'avg_volume': kline_data.get('avg_vol', 0),
                            'data_source': 'realtime'
                        }
                    )
                    yield event
                    self.last_update = datetime.now()
                
                # 等待下次更新
                time.sleep(self.interval)
                
            except Exception as e:
                self.logger.error(f"Error fetching real-time data: {e}")
                time.sleep(self.interval)
    
    def _fetch_latest_data(self) -> Optional[Dict[str, Any]]:
        """
        获取最新K线数据
        
        Returns:
            Dict: K线数据字典，失败返回None
        """
        try:
            # 使用akshare获取实时行情
            stock_df = ak.stock_bid_ask_em(symbol=self.symbol)
            
            if stock_df.empty:
                self.logger.warning(f"No real-time data for {self.symbol}")
                return None
            
            # 将item列设为索引
            stock_data = stock_df.set_index('item')['value']
            
            # 格式化数据
            kline = {
                'date': datetime.now(),
                'open': float(stock_data['今开']),
                'high': float(stock_data['最高']),
                'low': float(stock_data['最低']),
                'close': float(stock_data['最新']),
                'volume': int(stock_data['总手']),
                'avg_vol': 0  # 实时数据暂不计算
            }
            
            return kline
            
        except Exception as e:
            self.logger.error(f"Failed to fetch real-time data for {self.symbol}: {e}")
            return None
    
    def is_market_open(self) -> bool:
        """
        检查市场是否开放
        
        Returns:
            bool: 市场是否开放
        """
        now = datetime.now()
        
        # 简化的市场开放时间检查（工作日9:30-15:00）
        if now.weekday() >= 5:  # 周末
            return False
        
        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=15, minute=0, second=0, microsecond=0)
        
        return market_open <= now <= market_close
    
    def stop(self):
        """
        停止数据流
        """
        self._running = False
        self.logger.info(f"Stopped real-time data provider for {self.symbol}")
