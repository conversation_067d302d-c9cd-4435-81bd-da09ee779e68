"""
执行处理器基类

定义订单执行的统一接口。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime

from ..core.event import SignalEvent, OrderEvent, FillEvent


class BaseExecutionHandler(ABC):
    """
    执行处理器基类
    
    定义订单执行的统一接口，支持不同的执行环境（回测、实盘等）。
    """
    
    def __init__(self, name: str):
        """
        初始化执行处理器
        
        Args:
            name: 处理器名称
        """
        self.name = name
        self.logger = logging.getLogger(__name__)
        
        # 执行统计
        self.stats = {
            'orders_created': 0,
            'orders_filled': 0,
            'orders_rejected': 0,
            'total_commission': 0.0
        }
    
    @abstractmethod
    def process_signal(self, signal_event: SignalEvent) -> List[OrderEvent]:
        """
        处理交易信号，生成订单
        
        Args:
            signal_event: 交易信号事件
            
        Returns:
            List[OrderEvent]: 生成的订单列表
        """
        pass
    
    @abstractmethod
    def execute_order(self, order_event: OrderEvent) -> Optional[FillEvent]:
        """
        执行订单
        
        Args:
            order_event: 订单事件
            
        Returns:
            Optional[FillEvent]: 成交事件，如果未成交返回None
        """
        pass
    
    def calculate_commission(self, quantity: int, price: float) -> float:
        """
        计算手续费
        
        Args:
            quantity: 交易数量
            price: 交易价格
            
        Returns:
            float: 手续费金额
        """
        # 默认手续费计算（可在子类中重写）
        trade_value = quantity * price
        commission_rate = 0.0003  # 0.03%
        min_commission = 5.0      # 最低5元
        
        commission = max(trade_value * commission_rate, min_commission)
        return round(commission, 2)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取执行统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'handler_name': self.name,
            **self.stats
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'orders_created': 0,
            'orders_filled': 0,
            'orders_rejected': 0,
            'total_commission': 0.0
        }
    
    def _create_order_from_signal(self, signal_event: SignalEvent, 
                                 order_type: str = 'MARKET') -> OrderEvent:
        """
        从信号创建订单
        
        Args:
            signal_event: 交易信号事件
            order_type: 订单类型
            
        Returns:
            OrderEvent: 订单事件
        """
        # 生成订单ID
        order_id = f"{signal_event.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        order = OrderEvent(
            symbol=signal_event.symbol,
            timestamp=signal_event.timestamp,
            order_type=order_type,
            direction=signal_event.signal_type,
            quantity=signal_event.quantity,
            price=signal_event.price,
            order_id=order_id,
            metadata={
                'signal_reason': signal_event.reason,
                'signal_confidence': signal_event.confidence,
                'strategy': signal_event.metadata.get('strategy', 'Unknown')
            }
        )
        
        self.stats['orders_created'] += 1
        return order
    
    def _create_fill_from_order(self, order_event: OrderEvent, 
                               fill_price: float, fill_quantity: int) -> FillEvent:
        """
        从订单创建成交事件
        
        Args:
            order_event: 订单事件
            fill_price: 成交价格
            fill_quantity: 成交数量
            
        Returns:
            FillEvent: 成交事件
        """
        # 计算手续费
        commission = self.calculate_commission(fill_quantity, fill_price)
        
        # 生成成交ID
        fill_id = f"FILL_{order_event.order_id}_{datetime.now().strftime('%f')}"
        
        fill = FillEvent(
            symbol=order_event.symbol,
            timestamp=datetime.now(),
            direction=order_event.direction,
            quantity=fill_quantity,
            fill_price=fill_price,
            commission=commission,
            order_id=order_event.order_id,
            fill_id=fill_id,
            metadata={
                'order_type': order_event.order_type,
                'execution_handler': self.name,
                **order_event.metadata
            }
        )
        
        self.stats['orders_filled'] += 1
        self.stats['total_commission'] += commission
        
        return fill
    
    def _reject_order(self, order_event: OrderEvent, reason: str):
        """
        拒绝订单
        
        Args:
            order_event: 订单事件
            reason: 拒绝原因
        """
        self.stats['orders_rejected'] += 1
        self.logger.warning(f"Order {order_event.order_id} rejected: {reason}")


class OrderValidator:
    """
    订单验证器
    
    验证订单的合法性。
    """
    
    @staticmethod
    def validate_order(order_event: OrderEvent) -> tuple[bool, str]:
        """
        验证订单
        
        Args:
            order_event: 订单事件
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        # 检查基本字段
        if not order_event.symbol:
            return False, "Symbol is required"
        
        if order_event.quantity <= 0:
            return False, "Quantity must be positive"
        
        if order_event.direction not in ['BUY', 'SELL']:
            return False, "Direction must be BUY or SELL"
        
        if order_event.order_type not in ['MARKET', 'LIMIT', 'STOP']:
            return False, "Invalid order type"
        
        # 限价单必须有价格
        if order_event.order_type == 'LIMIT' and order_event.price is None:
            return False, "Limit order requires price"
        
        # 价格检查
        if order_event.price is not None and order_event.price <= 0:
            return False, "Price must be positive"
        
        return True, ""
