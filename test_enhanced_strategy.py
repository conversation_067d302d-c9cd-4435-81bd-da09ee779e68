#!/usr/bin/env python3
"""
测试增强转折点策略

验证基于量价分析的交易策略效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import List, Dict, Any

from trading_system.strategies.enhanced_turn_point_strategy import EnhancedTurnPointStrategy
from trading_system.core.event import MarketEvent, SignalEvent

def generate_realistic_data(days: int = 100) -> pd.DataFrame:
    """生成更真实的测试数据"""
    np.random.seed(42)
    
    base_price = 100.0
    prices = [base_price]
    volumes = []
    
    for i in range(days):
        # 生成更复杂的价格走势
        if i < 25:
            # 上升趋势
            trend = 0.015 + 0.01 * np.sin(i * 0.2)
        elif i < 50:
            # 下降趋势
            trend = -0.02 + 0.005 * np.sin(i * 0.3)
        elif i < 75:
            # 震荡整理
            trend = 0.005 * np.sin(i * 0.5)
        else:
            # 再次上升
            trend = 0.02 + 0.008 * np.sin(i * 0.15)
        
        # 添加随机噪声
        noise = np.random.normal(0, 0.015)
        change = trend + noise
        
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
        
        # 生成成交量（与价格变化和特殊事件相关）
        price_change = abs(change)
        base_volume = 1000000
        volume_multiplier = 1 + price_change * 20
        
        # 在重要转折点添加高成交量
        if i in [23, 24, 25, 26, 48, 49, 50, 51, 73, 74, 75, 76]:
            volume_multiplier *= 3.5
        elif i % 12 == 0:  # 定期高成交量
            volume_multiplier *= 2
        
        volume = base_volume * volume_multiplier * (1 + np.random.normal(0, 0.25))
        volumes.append(max(volume, base_volume * 0.2))
    
    # 创建DataFrame
    dates = [datetime.now() - timedelta(days=days-i) for i in range(days+1)]
    
    df = pd.DataFrame({
        'date': dates,
        'close': prices
    })
    
    # 生成OHLC数据
    df['open'] = df['close'].shift(1).fillna(df['close'])
    df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.008, len(df)))
    df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.008, len(df)))
    df['volume'] = [1000000] + volumes
    
    return df.iloc[1:]

def test_enhanced_strategy():
    """测试增强转折点策略"""
    print("=== 测试增强转折点策略 ===")
    
    # 生成测试数据
    df = generate_realistic_data(80)
    print(f"生成了 {len(df)} 天的测试数据")
    
    # 创建策略
    strategy = EnhancedTurnPointStrategy("TEST")
    
    # 调整策略参数
    strategy.set_parameter('position_size', 100)
    strategy.set_parameter('min_volume_confirm_strength', 0.5)
    strategy.set_parameter('min_breakout_strength', 0.7)
    strategy.set_parameter('stop_loss_percent', 0.08)
    strategy.set_parameter('take_profit_percent', 0.15)
    strategy.set_parameter('max_position_hold_days', 15)
    
    # 处理数据并收集信号
    signals = []
    market_events = []
    
    for i, row in df.iterrows():
        market_event = MarketEvent(
            symbol="TEST",
            timestamp=row['date'],
            open=row['open'],
            high=row['high'],
            low=row['low'],
            close=row['close'],
            volume=row['volume']
        )
        
        market_events.append(market_event)
        
        # 处理事件并获取信号
        generated_signals = strategy.on_market_event(market_event)
        
        for signal in generated_signals:
            signals.append(signal)
            print(f"\n{signal.timestamp.strftime('%Y-%m-%d')}: {signal.signal_type} @ {signal.price:.2f}")
            print(f"  原因: {signal.reason}")
            print(f"  置信度: {signal.confidence:.2f}")
            print(f"  数量: {signal.quantity}")
    
    print(f"\n总共生成 {len(signals)} 个交易信号")
    
    # 显示策略统计
    stats = strategy.get_stats()
    print(f"\n策略统计:")
    print(f"  当前持仓: {stats['current_position']}")
    print(f"  信号统计: {stats['signal_stats']}")
    print(f"  检测器统计: {stats['detector_stats']}")
    
    # 显示市场分析
    analysis = strategy.get_market_analysis()
    print(f"\n市场分析:")
    if analysis.get('volume_analysis'):
        va = analysis['volume_analysis']
        print(f"  当前量比: {va['volume_ratio']:.2f}")
        print(f"  成交量趋势: {va['volume_trend']:.4f}")
    
    if analysis.get('support_resistance_levels'):
        print(f"  支撑阻力位: {len(analysis['support_resistance_levels'])}个")
        for i, level in enumerate(analysis['support_resistance_levels'][:3]):
            print(f"    {i+1}. {level['level_type']} {level['price']:.2f} (强度: {level['strength']:.2f})")
    
    return df, signals, strategy, market_events

def calculate_performance(df: pd.DataFrame, signals: List[SignalEvent]) -> Dict[str, Any]:
    """计算策略表现"""
    if not signals:
        return {'error': 'No signals to analyze'}
    
    trades = []
    position = 0
    entry_price = 0
    
    for signal in signals:
        if signal.signal_type == 'BUY':
            if position <= 0:  # 开多仓或平空仓
                if position < 0:  # 先平空仓
                    profit = (entry_price - signal.price) / entry_price
                    trades.append({
                        'type': 'SHORT',
                        'entry_price': entry_price,
                        'exit_price': signal.price,
                        'profit_pct': profit,
                        'signal': signal
                    })
                # 开多仓
                position = signal.quantity
                entry_price = signal.price
        
        elif signal.signal_type == 'SELL':
            if position >= 0:  # 平多仓或开空仓
                if position > 0:  # 先平多仓
                    profit = (signal.price - entry_price) / entry_price
                    trades.append({
                        'type': 'LONG',
                        'entry_price': entry_price,
                        'exit_price': signal.price,
                        'profit_pct': profit,
                        'signal': signal
                    })
                # 开空仓
                position = -signal.quantity
                entry_price = signal.price
    
    if not trades:
        return {'error': 'No completed trades'}
    
    # 计算统计指标
    profits = [trade['profit_pct'] for trade in trades]
    win_trades = [p for p in profits if p > 0]
    loss_trades = [p for p in profits if p <= 0]
    
    performance = {
        'total_trades': len(trades),
        'win_trades': len(win_trades),
        'loss_trades': len(loss_trades),
        'win_rate': len(win_trades) / len(trades) if trades else 0,
        'total_return': sum(profits),
        'avg_return': np.mean(profits),
        'max_profit': max(profits) if profits else 0,
        'max_loss': min(profits) if profits else 0,
        'sharpe_ratio': np.mean(profits) / np.std(profits) if len(profits) > 1 and np.std(profits) > 0 else 0,
        'trades': trades
    }
    
    return performance

def plot_strategy_results(df: pd.DataFrame, signals: List[SignalEvent], strategy):
    """绘制策略结果"""
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
    
    # 价格图
    ax1.plot(df['date'], df['close'], 'b-', linewidth=1, label='Price')
    
    # 标记交易信号
    buy_signals = [s for s in signals if s.signal_type == 'BUY']
    sell_signals = [s for s in signals if s.signal_type == 'SELL']
    
    if buy_signals:
        buy_dates = [s.timestamp for s in buy_signals]
        buy_prices = [s.price for s in buy_signals]
        ax1.scatter(buy_dates, buy_prices, color='green', marker='^', s=100, zorder=5, label='BUY')
        
        for signal in buy_signals:
            ax1.annotate(f'BUY\n{signal.price:.1f}', 
                        (signal.timestamp, signal.price),
                        xytext=(10, 10), textcoords='offset points',
                        fontsize=8, ha='left', color='green')
    
    if sell_signals:
        sell_dates = [s.timestamp for s in sell_signals]
        sell_prices = [s.price for s in sell_signals]
        ax1.scatter(sell_dates, sell_prices, color='red', marker='v', s=100, zorder=5, label='SELL')
        
        for signal in sell_signals:
            ax1.annotate(f'SELL\n{signal.price:.1f}', 
                        (signal.timestamp, signal.price),
                        xytext=(10, -10), textcoords='offset points',
                        fontsize=8, ha='left', color='red')
    
    # 标记支撑阻力位
    analysis = strategy.get_market_analysis()
    if analysis.get('support_resistance_levels'):
        for level in analysis['support_resistance_levels'][:3]:
            color = 'orange' if level['level_type'] == 'RESISTANCE' else 'purple'
            ax1.axhline(y=level['price'], color=color, linestyle='--', alpha=0.7, linewidth=1)
            ax1.text(df['date'].iloc[-1], level['price'], 
                    f"{level['level_type'][:3]} {level['price']:.1f}", 
                    fontsize=8, ha='right', va='bottom')
    
    ax1.set_title('Enhanced Turn Point Strategy - Price & Signals')
    ax1.set_ylabel('Price')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 成交量图
    ax2.bar(df['date'], df['volume'], alpha=0.7, color='blue', width=0.8)
    avg_volume = df['volume'].mean()
    ax2.axhline(y=avg_volume, color='red', linestyle='--', alpha=0.7, label=f'Avg Volume: {avg_volume:,.0f}')
    ax2.set_title('Volume Analysis')
    ax2.set_ylabel('Volume')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 信号置信度图
    if signals:
        signal_dates = [s.timestamp for s in signals]
        signal_confidence = [s.confidence for s in signals]
        signal_colors = ['green' if s.signal_type == 'BUY' else 'red' for s in signals]
        
        ax3.scatter(signal_dates, signal_confidence, c=signal_colors, s=60, alpha=0.7)
        ax3.axhline(y=0.5, color='gray', linestyle='--', alpha=0.5, label='Threshold')
        ax3.set_title('Signal Confidence')
        ax3.set_ylabel('Confidence')
        ax3.set_xlabel('Date')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    
    # 格式化x轴日期
    for ax in [ax1, ax2, ax3]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=5))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    # 运行测试
    df, signals, strategy, market_events = test_enhanced_strategy()
    
    # 计算表现
    performance = calculate_performance(df, signals)
    if 'error' not in performance:
        print(f"\n策略表现:")
        print(f"  总交易次数: {performance['total_trades']}")
        print(f"  胜率: {performance['win_rate']:.2%}")
        print(f"  总收益率: {performance['total_return']:.2%}")
        print(f"  平均收益率: {performance['avg_return']:.2%}")
        print(f"  最大盈利: {performance['max_profit']:.2%}")
        print(f"  最大亏损: {performance['max_loss']:.2%}")
        print(f"  夏普比率: {performance['sharpe_ratio']:.2f}")
    else:
        print(f"\n无法计算表现: {performance['error']}")
    
    # 绘制结果
    print("\n正在生成图表...")
    plot_strategy_results(df, signals, strategy)
    
    print("\n测试完成！")
