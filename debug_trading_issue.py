#!/usr/bin/env python3
"""
调试交易问题 - 分析为什么只有卖出没有买入
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
from datetime import datetime
from typing import Dict, Any

from trading_system.core import EventEngine, MarketEvent, SignalEvent, OrderEvent, FillEvent
from trading_system.data import HistoricalDataProvider
from trading_system.analysis import IndicatorCalculator
from trading_system.strategies import FixedEnhancedTurnPointStrategy
from trading_system.execution import BacktestExecutionHandler
from trading_system.portfolio import PortfolioManager

class TradingDebugger:
    """交易调试器"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        
        # 核心组件
        self.event_engine = EventEngine()
        self.data_provider = HistoricalDataProvider(
            symbol='003021',
            start_date='20250101',
            end_date='20250630'
        )
        self.indicator_calculator = IndicatorCalculator()
        self.strategy = FixedEnhancedTurnPointStrategy('003021')
        self.execution_handler = BacktestExecutionHandler(
            event_engine=self.event_engine,
            slippage_pct=0.001
        )
        self.portfolio_manager = PortfolioManager(initial_cash=100000)
        
        # 调试统计
        self.debug_stats = {
            'buy_signals': 0,
            'sell_signals': 0,
            'buy_orders': 0,
            'sell_orders': 0,
            'buy_fills': 0,
            'sell_fills': 0,
            'pending_buy_orders': 0,
            'pending_sell_orders': 0
        }
        
        # 注册事件处理器
        self._register_handlers()
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def _register_handlers(self):
        """注册事件处理器"""
        self.event_engine.register_handler(MarketEvent, self._on_market_event)
        self.event_engine.register_handler(SignalEvent, self._on_signal_event)
        self.event_engine.register_handler(OrderEvent, self._on_order_event)
        self.event_engine.register_handler(FillEvent, self._on_fill_event)
    
    def _on_market_event(self, event: MarketEvent):
        """处理市场数据事件"""
        # 计算技术指标
        indicators = self.indicator_calculator.calculate_all(event)
        event.indicators.update(indicators)
        
        # 策略处理
        signals = self.strategy.on_market_event(event)
        for signal in signals:
            self.event_engine.put_event(signal)
        
        # 更新执行处理器的市场数据
        self.execution_handler.update_market_data(event)
        
        # 更新投资组合市值
        self.portfolio_manager.update_market_value(event)
    
    def _on_signal_event(self, event: SignalEvent):
        """处理交易信号事件"""
        if event.signal_type == 'BUY':
            self.debug_stats['buy_signals'] += 1
            price_str = f"{event.price:.2f}" if event.price is not None else "MARKET"
            self.logger.info(f"🟢 BUY SIGNAL: {event.quantity} @ {price_str} - {event.reason}")
        else:
            self.debug_stats['sell_signals'] += 1
            price_str = f"{event.price:.2f}" if event.price is not None else "MARKET"
            self.logger.info(f"🔴 SELL SIGNAL: {event.quantity} @ {price_str} - {event.reason}")
        
        # 处理信号
        orders = self.execution_handler.process_signal(event)
        for order in orders:
            self.event_engine.put_event(order)
    
    def _on_order_event(self, event: OrderEvent):
        """处理订单事件"""
        if event.direction == 'BUY':
            self.debug_stats['buy_orders'] += 1
            price_str = f"{event.price:.2f}" if event.price is not None else "MARKET"
            self.logger.info(f"🟢 BUY ORDER: {event.quantity} @ {price_str} ({event.order_type})")
        else:
            self.debug_stats['sell_orders'] += 1
            price_str = f"{event.price:.2f}" if event.price is not None else "MARKET"
            self.logger.info(f"🔴 SELL ORDER: {event.quantity} @ {price_str} ({event.order_type})")
        
        # 执行订单
        fill_event = self.execution_handler.execute_order(event)
        if fill_event:
            self.event_engine.put_event(fill_event)
        else:
            # 订单未立即执行，可能是限价单待执行
            if event.direction == 'BUY':
                self.debug_stats['pending_buy_orders'] += 1
                price_str = f"{event.price:.2f}" if event.price is not None else "MARKET"
                self.logger.warning(f"⏳ BUY ORDER PENDING: {event.quantity} @ {price_str}")
            else:
                self.debug_stats['pending_sell_orders'] += 1
                price_str = f"{event.price:.2f}" if event.price is not None else "MARKET"
                self.logger.warning(f"⏳ SELL ORDER PENDING: {event.quantity} @ {price_str}")
    
    def _on_fill_event(self, event: FillEvent):
        """处理成交事件"""
        if event.direction == 'BUY':
            self.debug_stats['buy_fills'] += 1
            self.logger.info(f"✅ BUY FILLED: {event.quantity} @ {event.fill_price:.2f}")
        else:
            self.debug_stats['sell_fills'] += 1
            self.logger.info(f"✅ SELL FILLED: {event.quantity} @ {event.fill_price:.2f}")
        
        # 处理成交
        self.portfolio_manager.process_fill_event(event)
    
    def run_debug(self, max_events=50):
        """运行调试"""
        self.logger.info("开始调试交易问题...")
        
        # 启动事件引擎
        self.event_engine.start()
        
        try:
            event_count = 0
            for market_event in self.data_provider.get_data_stream():
                if event_count >= max_events:
                    break
                
                self.event_engine.put_event(market_event)
                event_count += 1
                
                # 每10个事件打印一次统计
                if event_count % 10 == 0:
                    self._print_debug_stats()
            
            # 等待所有事件处理完成
            self.event_engine.wait_for_completion(timeout=10)
            
            # 最终统计
            self._print_final_stats()
            
        finally:
            self.event_engine.stop()
    
    def _print_debug_stats(self):
        """打印调试统计"""
        stats = self.debug_stats
        self.logger.info(f"📊 当前统计: 买入信号={stats['buy_signals']}, 卖出信号={stats['sell_signals']}, "
                        f"买入订单={stats['buy_orders']}, 卖出订单={stats['sell_orders']}, "
                        f"买入成交={stats['buy_fills']}, 卖出成交={stats['sell_fills']}")
    
    def _print_final_stats(self):
        """打印最终统计"""
        stats = self.debug_stats
        
        print("\n" + "=" * 80)
        print("交易调试结果分析")
        print("=" * 80)
        
        print(f"📊 信号统计:")
        print(f"  买入信号: {stats['buy_signals']}")
        print(f"  卖出信号: {stats['sell_signals']}")
        
        print(f"\n📋 订单统计:")
        print(f"  买入订单: {stats['buy_orders']}")
        print(f"  卖出订单: {stats['sell_orders']}")
        print(f"  待执行买入订单: {stats['pending_buy_orders']}")
        print(f"  待执行卖出订单: {stats['pending_sell_orders']}")
        
        print(f"\n✅ 成交统计:")
        print(f"  买入成交: {stats['buy_fills']}")
        print(f"  卖出成交: {stats['sell_fills']}")
        
        # 分析问题
        print(f"\n🔍 问题分析:")
        if stats['buy_signals'] > 0 and stats['buy_fills'] == 0:
            print("  ❌ 问题确认: 有买入信号但没有买入成交")
            print("  🔍 可能原因: 买入限价单价格设置问题")
        
        if stats['pending_buy_orders'] > 0:
            print(f"  ⏳ 有 {stats['pending_buy_orders']} 个买入订单待执行")
            print("  🔍 可能原因: 限价单价格低于市场价格")
        
        if stats['sell_fills'] > stats['buy_fills']:
            print("  ❌ 问题确认: 卖出成交多于买入成交，导致持仓不平衡")
        
        # 检查待执行订单
        pending_orders = self.execution_handler.get_pending_orders()
        if pending_orders:
            print(f"\n📋 当前待执行订单 ({len(pending_orders)}个):")
            for order in pending_orders[:5]:  # 只显示前5个
                print(f"  {order.direction} {order.quantity} @ ¥{order.price:.2f}")
        
        print("=" * 80)

def main():
    """主函数"""
    print("交易问题调试器")
    print("分析为什么只有卖出记录没有买入记录")
    print("-" * 50)
    
    debugger = TradingDebugger()
    debugger.run_debug(max_events=100)  # 只处理前100个事件进行调试

if __name__ == "__main__":
    main()
