from detector import MarketEvent


class TradingSignalGenerator:
    def __init__(self):
        self.signals = []
    
    def __call__(self, event: MarketEvent):
        """事件处理器接口"""
        # 1. 基于转折点的信号
        if event.point_info:
            self._generate_point_based_signals(event)
        
        # 2. 基于技术指标的超买超卖信号
        self._generate_indicator_signals(event)
        
        # 3. 趋势跟踪信号
        self._generate_trend_signals(event)
        
        # 保存事件中的信号
        event.signals = self.signals.copy()
    
    def _generate_point_based_signals(self, event):
        """基于转折点生成信号"""
        point_type = event.point_info['type']
        
        if point_type == 'trough':
            self.signals.append({
                'date': event.date,
                'type': 'BUY',
                'price': event.close,
                'reason': f'Trough detected at {event.point_info["price"]:.2f}'
            })
            print(f"📈 买入信号 @ {event.date}: 检测到波谷 ({event.point_info['price']:.2f})")
        
        elif point_type == 'peak':
            self.signals.append({
                'date': event.date,
                'type': 'SELL',
                'price': event.close,
                'reason': f'Peak detected at {event.point_info["price"]:.2f}'
            })
            print(f"📉 卖出信号 @ {event.date}: 检测到波峰 ({event.point_info['price']:.2f})")
    
    def _generate_indicator_signals(self, event):
        """基于技术指标生成信号"""
        rsi = event.indicators.get('RSI', 50)
        ma5 = event.indicators.get('MA5', event.close)
        ma20 = event.indicators.get('MA20', event.close)
        
        # RSI超买超卖信号
        if rsi < 30:
            self.signals.append({
                'date': event.date,
                'type': 'BUY',
                'price': event.close,
                'reason': f'Oversold (RSI={rsi:.2f})'
            })
            print(f"📈 买入信号 @ {event.date}: RSI超卖 ({rsi:.2f})")
        elif rsi > 70:
            self.signals.append({
                'date': event.date,
                'type': 'SELL',
                'price': event.close,
                'reason': f'Overbought (RSI={rsi:.2f})'
            })
            print(f"📉 卖出信号 @ {event.date}: RSI超买 ({rsi:.2f})")
        
        # 均线交叉信号
        if ma5 > ma20 and ('MA5' in event.indicators and 'MA20' in event.indicators):
            self.signals.append({
                'date': event.date,
                'type': 'BUY',
                'price': event.close,
                'reason': f'Golden Cross (MA5={ma5:.2f} > MA20={ma20:.2f})'
            })
            print(f"📈 买入信号 @ {event.date}: 金叉 (MA5={ma5:.2f} > MA20={ma20:.2f})")
        elif ma5 < ma20 and ('MA5' in event.indicators and 'MA20' in event.indicators):
            self.signals.append({
                'date': event.date,
                'type': 'SELL',
                'price': event.close,
                'reason': f'Death Cross (MA5={ma5:.2f} < MA20={ma20:.2f})'
            })
            print(f"📉 卖出信号 @ {event.date}: 死叉 (MA5={ma5:.2f} < MA20={ma20:.2f})")
    
    def _generate_trend_signals(self, event):
        """趋势跟踪信号（示例）"""
        # 这里可以添加更复杂的趋势判断逻辑
        pass