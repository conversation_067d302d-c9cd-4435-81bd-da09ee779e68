# API 参考文档

本文档详细描述了交易系统的各个模块的API。

## 目录

1. [Core 模块](#1-core-模块)
2. [Data 模块](#2-data-模块)
3. [Analysis 模块](#3-analysis-模块)
4. [Strategies 模块](#4-strategies-模块)
5. [Execution 模块](#5-execution-模块)
6. [Portfolio 模块](#6-portfolio-模块)
7. [Utils 模块](#7-utils-模块)

## 1. Core 模块

### 1.1 EventEngine

事件引擎，负责事件的注册、分发和处理。

```python
class EventEngine:
    def __init__(self):
        """初始化事件引擎"""
        
    def start(self):
        """启动事件引擎"""
        
    def stop(self):
        """停止事件引擎"""
        
    def register_handler(self, event_type: Type, handler: Callable):
        """注册事件处理器"""
        
    def put_event(self, event: Event):
        """放入事件到队列"""
        
    def wait_for_completion(self, timeout: int = 30):
        """等待所有事件处理完成"""
```

### 1.2 事件类型

系统中定义的各种事件类型。

```python
class Event:
    """事件基类"""
    
class MarketEvent(Event):
    """
    市场数据事件
    
    属性:
        timestamp: 时间戳
        symbol: 交易标的
        open: 开盘价
        high: 最高价
        low: 最低价
        close: 收盘价
        volume: 成交量
        indicators: 技术指标字典
    """

class TurnPointEvent(Event):
    """
    转折点事件
    
    属性:
        timestamp: 时间戳
        direction: 转折方向 ("up" 或 "down")
        price: 转折点价格
        extent: 波动幅度
    """

class SignalEvent(Event):
    """
    交易信号事件
    
    属性:
        timestamp: 时间戳
        symbol: 交易标的
        signal_type: 信号类型 ("LONG" 或 "SHORT")
        strength: 信号强度
        price: 信号价格
    """

class OrderEvent(Event):
    """
    订单事件
    
    属性:
        timestamp: 时间戳
        symbol: 交易标的
        order_type: 订单类型
        quantity: 数量
        direction: 方向
        price: 价格
    """

class FillEvent(Event):
    """
    成交事件
    
    属性:
        timestamp: 时间戳
        symbol: 交易标的
        quantity: 成交数量
        direction: 成交方向
        price: 成交价格
        commission: 手续费
    """
```

## 2. Data 模块

### 2.1 HistoricalDataProvider

历史数据提供者。

```python
class HistoricalDataProvider:
    def __init__(self, symbol: str, start_date: str, end_date: str):
        """
        初始化数据提供者
        
        Args:
            symbol: 交易标的
            start_date: 开始日期，格式: "YYYYMMDD"
            end_date: 结束日期，格式: "YYYYMMDD"
        """
        
    def get_data_stream(self) -> Generator[MarketEvent, None, None]:
        """
        获取数据流
        
        Yields:
            MarketEvent: 市场数据事件
        """
```

## 3. Analysis 模块

### 3.1 IndicatorCalculator

技术指标计算器。

```python
class IndicatorCalculator:
    def __init__(self):
        """初始化指标计算器"""
        
    def create_default_indicators(self):
        """创建默认指标"""
        
    def calculate_all(self, market_event: MarketEvent) -> Dict[str, float]:
        """
        计算所有指标
        
        Args:
            market_event: 市场数据事件
            
        Returns:
            Dict[str, float]: 指标结果字典
        """
```

### 3.2 TurnPointDetector

转折点检测器。

```python
class TurnPointDetector:
    def __init__(self, min_keep_days: int = 3, min_extent_percent: float = 1.0):
        """
        初始化转折点检测器
        
        Args:
            min_keep_days: 最小持仓天数
            min_extent_percent: 最小波动百分比
        """
        
    def process_market_event(self, event: MarketEvent) -> Optional[TurnPointEvent]:
        """
        处理市场数据事件
        
        Args:
            event: 市场数据事件
            
        Returns:
            Optional[TurnPointEvent]: 转折点事件（如果检测到）
        """
```

## 4. Strategies 模块

### 4.1 BaseStrategy

策略基类。

```python
class BaseStrategy:
    def __init__(self):
        """初始化策略"""
        
    def process_event(self, event: Event) -> List[SignalEvent]:
        """
        处理事件
        
        Args:
            event: 输入事件
            
        Returns:
            List[SignalEvent]: 生成的交易信号列表
        """
```

## 5. Execution 模块

### 5.1 BacktestExecutionHandler

回测执行处理器。

```python
class BacktestExecutionHandler:
    def __init__(self, slippage_pct: float = 0.001):
        """
        初始化回测执行处理器
        
        Args:
            slippage_pct: 滑点百分比
        """
        
    def process_signal(self, signal: SignalEvent) -> List[OrderEvent]:
        """
        处理交易信号
        
        Args:
            signal: 交易信号事件
            
        Returns:
            List[OrderEvent]: 生成的订单列表
        """
        
    def execute_order(self, order: OrderEvent) -> Optional[FillEvent]:
        """
        执行订单
        
        Args:
            order: 订单事件
            
        Returns:
            Optional[FillEvent]: 成交事件（如果成交）
        """
```

## 6. Portfolio 模块

### 6.1 PortfolioManager

投资组合管理器。

```python
class PortfolioManager:
    def __init__(self, initial_cash: float = 100000):
        """
        初始化投资组合管理器
        
        Args:
            initial_cash: 初始资金
        """
        
    def update_market_value(self, event: MarketEvent):
        """
        更新投资组合市值
        
        Args:
            event: 市场数据事件
        """
        
    def process_fill_event(self, event: FillEvent):
        """
        处理成交事件
        
        Args:
            event: 成交事件
        """
        
    def get_performance_stats(self) -> Dict[str, float]:
        """
        获取绩效统计
        
        Returns:
            Dict[str, float]: 绩效指标字典
        """
```

## 7. Utils 模块

### 7.1 TradingVisualizer

交易可视化工具。

```python
class TradingVisualizer:
    def print_performance_report(self, portfolio_manager: PortfolioManager):
        """
        打印绩效报告
        
        Args:
            portfolio_manager: 投资组合管理器
        """
        
    def plot_trading_results(self, 
                           portfolio_manager: PortfolioManager,
                           market_data: pd.DataFrame,
                           turn_points: List[TurnPointEvent],
                           save_path: Optional[str] = None):
        """
        绘制交易结果图表
        
        Args:
            portfolio_manager: 投资组合管理器
            market_data: 市场数据
            turn_points: 转折点列表
            save_path: 保存路径（可选）
        """
        
    def plot_performance_metrics(self,
                               portfolio_manager: PortfolioManager,
                               save_path: Optional[str] = None):
        """
        绘制绩效指标图表
        
        Args:
            portfolio_manager: 投资组合管理器
            save_path: 保存路径（可选）
        """
```

## 异常类型

系统定义的异常类型。

```python
class TradingSystemException(Exception):
    """交易系统基础异常"""

class InvalidConfigError(TradingSystemException):
    """无效配置异常"""

class DataError(TradingSystemException):
    """数据错误异常"""

class ExecutionError(TradingSystemException):
    """执行错误异常"""
```

## 配置示例

```python
DEFAULT_CONFIG = {
    # 基础配置
    'symbol': '003021',          # 交易标的
    'start_date': '20250101',    # 回测起始日期
    'end_date': '20250630',      # 回测结束日期
    'initial_cash': 100000,      # 初始资金
    
    # 策略参数
    'min_keep_days': 3,          # 最小持仓天数
    'min_extent_percent': 5,     # 最小波动百分比
    
    # 交易成本
    'slippage_pct': 0.001,       # 滑点率
    'commission_rate': 0.0003    # 手续费率
}
```

## 使用示例

```python
# 创建并配置系统
config = DEFAULT_CONFIG.copy()
system = TradingSystem(config)

# 初始化
system.initialize()

# 运行回测
system.run_backtest()

# 获取结果
results = system.get_results()

# 显示结果
system.show_results(save_charts=True)
